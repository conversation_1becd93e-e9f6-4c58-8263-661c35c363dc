<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@/components/ui/sidebar';
import { Link, router } from '@inertiajs/vue3';
import { BadgeCheck, ChevronsUpDown, LogOut } from 'lucide-vue-next';
import Swal from 'sweetalert2';

const props = defineProps<{
    user: {
        name: string;
        email: string;
        avatar: string;
    };
}>();

const { isMobile } = useSidebar();

const handleLogout = (event: Event) => {
    event.preventDefault();

    Swal.fire({
        title: 'Logout',
        text: 'Do you want logout of this system?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, log out',
        buttonsStyling: false, // disable default SweetAlert2 styles
        customClass: {
            confirmButton: 'bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded',
            cancelButton: 'bg-red-600 hover:bg-red-400 text-white px-4 py-2 rounded ml-2',
        },
    }).then((result) => {
        if (result.isConfirmed) {
            router.post(route('logout'));
        }
    });
};
</script>

<template>
    <SidebarMenu>
        <SidebarMenuItem>
            <DropdownMenu>
                <DropdownMenuTrigger as-child>
                    <SidebarMenuButton size="lg" class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
                        <Avatar>
                            <AvatarImage :src="props.user.avatar" :alt="props.user.name" />
                            <AvatarFallback class="text-sidebar-accent-foreground">
                                <FaIcon name="user" />
                            </AvatarFallback>
                        </Avatar>
                        <div class="grid flex-1 text-left text-sm leading-tight">
                            <span class="truncate text-sm font-bold">{{ props.user.name }}</span>
                        </div>
                        <ChevronsUpDown class="ml-auto size-4" />
                    </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                    class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                    :side="isMobile ? 'bottom' : 'right'"
                    align="end"
                    :side-offset="4"
                >
                    <DropdownMenuLabel class="p-0 font-normal">
                        <div class="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                            <Avatar class="h-8 w-8 rounded-lg">
                                <AvatarImage :src="props.user.avatar" :alt="props.user.name" />
                                <AvatarFallback class="rounded-lg"> <FaIcon name="user" /> </AvatarFallback>
                            </Avatar>
                            <div class="grid flex-1 text-left text-sm leading-tight">
                                <span class="truncate font-semibold">{{ props.user.name }}</span>
                                <span v-if="props.user.email" class="truncate text-xs">{{ props.user.email }}</span>
                            </div>
                        </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                        <DropdownMenuItem :as-child="true">
                            <Link class="block w-full" :href="route('profile.edit')">
                                <BadgeCheck />
                                Account
                            </Link>
                        </DropdownMenuItem>
                    </DropdownMenuGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem :as-child="true">
                        <button class="block w-full" @click="handleLogout">
                            <LogOut />
                            Log out
                        </button>
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </SidebarMenuItem>
    </SidebarMenu>
</template>
