<?php

namespace App\Models;

use App\Enums\Agent\AgentStatus;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * AgentProfile model for managing agent profiles
 */
class AgentProfile extends BaseModel
{
    use HasFactory, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'company_id',
        'code',
        'name',
        'display_name',
        'email',
        'status',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'status' => AgentStatus::class,
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Set the display name and automatically set the name to uppercase.
     *
     * @param  string  $value
     * @return void
     */
    public function setDisplayNameAttribute($value)
    {
        $this->attributes['display_name'] = $value;
        $this->attributes['name'] = strtoupper($value);
    }

    /**
     * Get the teams associated with this agent profile.
     */
    public function teams(): MorphToMany
    {
        return $this->morphToMany(Team::class, 'teamable');
    }

    /**
     * Get the companies associated with this agent profile.
     */
    public function companies(): MorphToMany
    {
        return $this->morphToMany(Company::class, 'companyable');
    }

    /**
     * Get the headquarter associated with this agent profile.
     */
    public function headquarter(): ?Headquarter
    {
        return $this->companies()->where('is_headquarter', true)->first();
    }

    /**
     * Get the company (non-headquarter) associated with this agent profile.
     */
    public function company(): ?Company
    {
        return $this->companies()->whereNotNull('headquarter_id')->first();
    }

    /**
     * Get the loans associated with this agent profile.
     */
    public function loans(): HasMany
    {
        return $this->hasMany(Loan::class, 'agent_id');
    }

    /**
     * Get the user's resolve hierarchy.
     */
    public function getResolveHierarchy(): array
    {
        $agentProfile = $this;

        if (! $this) {
            return ['headquarter' => null, 'company' => null];
        }

        if ($directCompany = $agentProfile->companies->first()) {
            $headquarter = ($directCompany->headquarter_id && $directCompany->parent)
                ? $directCompany->parent
                : null;

            return [
                'headquarter' => $headquarter,
                'company' => $directCompany,
            ];
        }

        return [
            'headquarter' => null,
            'company' => null,
        ];
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new headquarter.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix(padLength: 5);
    }

    /**
     * Scope a query to get agent profiles for dropdown lists.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDropdown($query)
    {
        return $query->select('id', 'name', 'code', 'email', 'status')
            ->orderBy('name');
    }

    /**
     * Get agent profiles for dropdown selection
     *
     * @param  array  $columns  Additional columns to select
     * @return array Formatted agent profile data
     */
    public static function getForDropdown(bool $includeInActive = false, array $columns = []): array
    {
        $defaultColumns = ['id', 'name', 'display_name', 'code', 'email', 'status'];
        $selectColumns = array_merge($defaultColumns, $columns);

        return self::forDropdown()
            ->select($selectColumns)
            ->with('companies:id,name')
            ->when(! $includeInActive, function ($query) {
                $query->where('status', AgentStatus::ACTIVE);
            })
            ->orderBy('name')
            ->get()
            ->map(function ($agent) {
                return [
                    'id' => $agent->id,
                    'name' => $agent->name,
                    'display_name' => $agent->display_name,
                    'code' => $agent->code,
                    'email' => $agent->email,
                    'status' => $agent->status->value,
                    'status_label' => $agent->status->label(),
                    'companies' => $agent->companies->map(fn ($company) => [
                        'id' => $company->id,
                        'name' => $company->name,
                    ])->toArray(),
                ];
            })
            ->toArray();
    }

    /**
     * Get headquarters accessible to the specified user.
     *
     * @param  \App\Models\User|null  $user
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function forUser($user = null)
    {
        $user = $user ?? auth()->user();

        if ($user->isSuperAdmin()) {
            return self::query();
        }

        return self::whereIn('company_id', $user->getAccessibleCompanyIds());
    }
}
