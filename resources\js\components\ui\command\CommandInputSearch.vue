<script setup lang="ts">
import type { HTMLAttributes } from 'vue';
import { reactiveOmit } from '@vueuse/core';
import { Search } from 'lucide-vue-next';
import { ListboxFilter, type ListboxFilterProps, useForwardProps } from 'reka-ui';
import { cn } from '@/lib/utils';
import { useCommand } from '.';

defineOptions({
    inheritAttrs: false
});

const props = defineProps<ListboxFilterProps & {
    class?: HTMLAttributes['class'];
    iconPosition?: string;
}>();

const delegatedProps = reactiveOmit(props, 'class');

const forwardedProps = useForwardProps(delegatedProps);

const emit = defineEmits(['update:modelValue']);
const handleChange = (value: string | number | (string | number)[]) => {
    emit('update:modelValue', value);
};
</script>

<template>
    <div
        data-slot="command-input-wrapper"
        class="flex h-12 items-center gap-2 border-b px-3 w-full"
    >
        <Search v-if="props.iconPosition === 'left'" class="size-4 shrink-0 opacity-50" />
        <ListboxFilter
            v-bind="{ ...forwardedProps, ...$attrs }"
            :model-value="props.modelValue"
            @update:model-value="handleChange"
            data-slot="command-input"
            auto-focus
            :class="cn('placeholder:text-muted-foreground flex h-12 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50', props.class)"
        />
        <Search v-if="props.iconPosition === 'right'" class="size-4 shrink-0 opacity-50" />
    </div>
</template>
