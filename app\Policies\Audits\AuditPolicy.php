<?php

namespace App\Policies\Audits;

use App\Enums\AccessControl\PermissionName;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use OwenIt\Auditing\Models\Audit;

class AuditPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any audits.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_AUDITS->value);
    }

    /**
     * Determine whether the user can view the audit.
     *
     * @return bool
     */
    public function view(User $user, Audit $audit)
    {
        return $user->hasPermissionTo(PermissionName::READ_AUDITS->value);
    }
}
