<?php

namespace Database\Factories;

use App\Enums\Selection\SelectionStatus;
use App\Models\Selection;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Selection>
 */
class SelectionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Selection::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $categories = ['country', 'state', 'city', 'address_type', 'document_type'];

        return [
            'uuid' => Str::uuid(),
            'category' => fake()->randomElement($categories),
            'value' => fake()->word(),
            'description' => fake()->optional(0.7)->sentence(),
            'sort_order' => fake()->numberBetween(1, 100),
            'status' => SelectionStatus::ACTIVE,
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Indicate that the selection is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SelectionStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the selection is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => SelectionStatus::INACTIVE,
        ]);
    }

    /**
     * Set the selection category.
     */
    public function category(string $category): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => $category,
        ]);
    }
}
