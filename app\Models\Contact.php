<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Contact model for managing contact information
 */
class Contact extends BaseModel
{
    use HasFactory;

    /**
     * Contact categories
     */
    public const CATEGORY_TELEPHONE = 1;

    public const CATEGORY_MOBILE = 2;

    public const CATEGORY_EMAIL = 3;

    public const CATEGORY_FAX = 4;

    public const CATEGORY_OTHER = 5;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'uuid',
        'contactable_type',
        'contactable_id',
        'category',
        'selection_type_id',
        'type',
        'selection_country_id',
        'country',
        'contact',
        'is_primary',
        'can_receive_sms',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'is_primary' => 'boolean',
            'selection_country_id' => 'integer',
            'can_receive_sms' => 'boolean',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the parent contactable model.
     */
    public function contactable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the country selection.
     */
    public function contactCountrySelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_country_id');
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->category)) {
                $model->category = self::CATEGORY_OTHER;
            }

            if (empty($model->is_primary)) {
                $model->is_primary = false;
            }

            if (empty($model->can_receive_sms)) {
                $model->can_receive_sms = false;
            }
        });
    }
}
