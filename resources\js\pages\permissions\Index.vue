<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Button } from '@/components/ui/button';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import type { Permission } from '@/types';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Props {
    permissions: PaginatedData<Permission>;
    filters: FilterOptions & {
        name?: string;
    };
}

const props = defineProps<Props>();

const form = useForm({});

const searchValue = ref(props.filters.name || '');

const debouncedSearch = useDebounceFn((value: string) => {
    form.get(route('permissions.index', { name: value }), {
        preserveState: true,
        preserveScroll: true,
        replace: true,
    });
}, 300);

const handleSearch = (value: string) => {
    debouncedSearch(value);
};

const handleReset = () => {
    searchValue.value = '';
    form.get(route('permissions.index'));
};

const handleEdit = (permission: Permission) => {
    form.get(route('permissions.edit', permission.id));
};

const handleView = (permission: Permission) => {
    form.get(route('permissions.show', permission.id));
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';

    form.get(
        route('permissions.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const handleDelete = (permissions: Permission) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Are you sure you want to delete this permission? This action cannot be undone.`,
        },
        submitOptions: {
            method: 'delete',
            url: route('permissions.destroy', permissions.id),
            transform: (data) => ({
                ...data,
            }),
            successMessage: `Permission deleted successfully.`,
            errorMessage: `Failed to delete permission. Please try again.`,
            entityName: 'permission',
        },
    });
};

const columns = [
    { field: 'name', label: 'Permission Name', sortable: true },
    { field: 'created_at', label: 'Created At', sortable: true, width: 'w-40', format: (value: string) => formatDateTime(value) },
    { field: 'updated_at', label: 'Updated At', sortable: true, width: 'w-40', format: (value: string) => formatDateTime(value) },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Permissions" />

        <div class="px-4 py-3">
            <Heading title="Permissions" pageNumber="P000032" />

            <SearchCard
                v-model:searchValue="searchValue"
                searchLabel="Permission Name"
                searchPlaceholder="Permission Name"
                @search="handleSearch"
                @reset="handleReset"
            >
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('permissions.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Permissions
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="permissions.data"
                        :sort-state="sortState"
                        empty-message="No permission found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        @delete="handleDelete"
                        :showDeleteButton="true"
                        :showStatusToggle="false"
                    >
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="permissions.from" :to="permissions.to" :total="permissions.total" entityName="permissions" />
                            </div>
                            <Pagination :links="permissions.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
