<?php

use App\Enums\Team\TeamStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teams', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('headquarter_id')->constrained('headquarters')->onDelete('cascade');
            $table->foreignId('company_id')->constrained('companies')->onDelete('cascade');
            $table->string('code', 36);
            $table->string('name');

            // Contact information
            $table->string('email')->nullable();
            $table->string('website')->nullable();
            $table->string('contact_no', 24)->nullable();
            $table->unsignedTinyInteger('status')->default(TeamStatus::ACTIVE->value);

            // Additional information
            $table->text('remark')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['headquarter_id', 'code']);
        });

        Schema::create('teamables', function (Blueprint $table) {
            $table->foreignId('team_id')->constrained('teams')->onDelete('cascade');
            $table->morphs('teamable');
            $table->primary(['team_id', 'teamable_id', 'teamable_type'], 'teamables_primary');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teamables');
        Schema::dropIfExists('teams');
    }
};
