<?php

namespace Database\Factories;

use App\Models\Collateral;
use App\Models\CollateralValuer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CollateralValuer>
 */
class CollateralValuerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CollateralValuer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'collateral_id' => Collateral::factory(),
            'valuation_amount' => fake()->randomFloat(2, 10000, 1000000),
            'valuer' => fake()->company(),
            'valuation_received_date' => fake()->dateTimeBetween('-1 year', 'now'),
            'land_search_received_date' => fake()->dateTimeBetween('-1 year', 'now'),
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Configure the model factory to associate with an existing collateral.
     */
    public function forCollateral(Collateral $collateral): static
    {
        return $this->state(fn (array $attributes) => [
            'collateral_id' => $collateral->id,
        ]);
    }
}
