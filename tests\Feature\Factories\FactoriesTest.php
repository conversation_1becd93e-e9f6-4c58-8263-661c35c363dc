<?php

namespace Tests\Feature\Factories;

use App\Models\AdminProfile;
use App\Models\AgentProfile;
use App\Models\Company;
use App\Models\Headquarter;
use App\Models\Locale;
use App\Models\Selection;
use App\Models\Team;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FactoriesTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_factory(): void
    {
        $user = User::factory()->create();
        $this->assertModelExists($user);
    }

    public function test_headquarter_factory(): void
    {
        $headquarter = Headquarter::factory()->create();
        $this->assertModelExists($headquarter);
        $this->assertNull($headquarter->headquarter_id);
    }

    public function test_company_factory(): void
    {
        $headquarter = Headquarter::factory()->create();
        $company = Company::factory()->forHeadquarter($headquarter)->create();

        $this->assertModelExists($company);
        $this->assertEquals($headquarter->id, $company->headquarter_id);
    }

    public function test_team_factory(): void
    {
        $team = Team::factory()->create();
        $this->assertModelExists($team);
    }

    public function test_admin_profile_factory(): void
    {
        $adminProfile = AdminProfile::factory()->create();
        $this->assertModelExists($adminProfile);
    }

    public function test_userables_factory(): void
    {
        $user = User::factory()->create();
        $adminProfile = AdminProfile::factory()->create();

        $adminProfile->users()->attach($user->id);

        $this->assertTrue($adminProfile->users->contains($user));
        $this->assertTrue($user->adminProfiles->contains($adminProfile));
    }

    public function test_companyables_factory(): void
    {
        $company = Company::factory()->create();
        $adminProfile = AdminProfile::factory()->create();

        $adminProfile->companies()->attach($company->id);

        $this->assertTrue($adminProfile->companies->contains($company));
        $this->assertTrue($company->adminProfiles->contains($adminProfile));
    }

    public function test_teamables_factory(): void
    {
        $team = Team::factory()->create();
        $adminProfile = AdminProfile::factory()->create();

        $adminProfile->teams()->attach($team->id);

        $this->assertTrue($adminProfile->teams->contains($team));
        $this->assertTrue($team->adminProfiles->contains($adminProfile));
    }

    public function test_selection_factory(): void
    {
        $selection = Selection::factory()->create();
        $this->assertModelExists($selection);
    }

    public function test_agent_profile_factory(): void
    {
        $agentProfile = AgentProfile::factory()->create();
        $this->assertModelExists($agentProfile);
    }

    public function test_locale_factory(): void
    {
        $locale = Locale::factory()->create();
        $this->assertModelExists($locale);
    }
}
