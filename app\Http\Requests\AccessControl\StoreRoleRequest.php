<?php

namespace App\Http\Requests\AccessControl;

use App\Http\Requests\BaseRequest;

class StoreRoleRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:roles,name',
            'level' => 'required|integer|min:0|max:999',
            'can_see_same_level' => 'nullable|boolean',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'The role name is required.',
            'name.unique' => 'This role name already exists.',
            'level.required' => 'The role level is required.',
            'level.integer' => 'The role level must be an integer.',
            'level.min' => 'The role level must be at least 0.',
            'level.max' => 'The role level must not exceed 999.',
            'permissions.*.exists' => 'One or more selected permissions are invalid.',
        ];
    }
}
