<?php

namespace App\Enums\Loan;

use App\Traits\EnumTrait;

enum LoanTxnStatus: int
{
    use EnumTrait;

    case UNPAID = 0;
    case PAID = 1;
    case PARTIALLY_PAID = 2;
    case CANCELLED = 3;

    /**
     * Get all available statuses as an array
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return [
            self::UNPAID->value => 'Unpaid',
            self::PAID->value => 'Paid',
            self::PARTIALLY_PAID->value => 'Partially Paid',
            self::CANCELLED->value => 'Cancelled',
        ];
    }

    /**
     * Get the display name for a status
     */
    public function label(): string
    {
        return match ($this) {
            self::UNPAID => 'Unpaid',
            self::PAID => 'Paid',
            self::PARTIALLY_PAID => 'Partially Paid',
            self::CANCELLED => 'Cancelled',
        };
    }

    /**
     * Get the description for a status
     */
    public function description(): string
    {
        return match ($this) {
            self::UNPAID => 'Transaction has not been paid',
            self::PAID => 'Transaction has been fully paid',
            self::PARTIALLY_PAID => 'Transaction has been partially paid',
            self::CANCELLED => 'Transaction has been cancelled',
        };
    }
}
