<script setup lang="ts">
import GlobalLoading from '@/components/loading/GlobalLoading.vue';
import { SidebarProvider } from '@/components/ui/sidebar';
import { SharedData } from '@/types';
import { usePage } from '@inertiajs/vue3';

interface Props {
    variant?: 'header' | 'sidebar';
}

defineProps<Props>();

const isOpen = usePage<SharedData>().props.sidebarOpen;
</script>

<template>
    <div v-if="variant === 'header'" class="flex min-h-screen w-full flex-col">
        <GlobalLoading />
        <slot />
    </div>
    <SidebarProvider v-else :default-open="isOpen">
        <GlobalLoading />
        <slot />
    </SidebarProvider>
</template>
