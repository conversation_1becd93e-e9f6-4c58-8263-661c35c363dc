<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Locale {
    id: number;
    uuid: string;
    word: string;
    en: string | null;
    zh: string | null;
    status: number;
    created_at: string;
    updated_at: string;
    created_by: {
        id: number;
        name: string;
    } | null;
    updated_by: {
        id: number;
        name: string;
    } | null;
}

interface Props {
    locales: PaginatedData<Locale>;
    filters: FilterOptions & {
        word?: string;
        zh?: string;
        en?: string;
        status?: number;
    };
    statuses: Record<number, string>;
}

const props = defineProps<Props>();
const form = useForm({});

const searchWord = ref(props.filters.word || '');
const searchZH = ref(props.filters.zh || '');
const searchEN = ref(props.filters.en || '');
const status = ref(props.filters.status || '');

const debouncedSearch = useDebounceFn(() => {
    form.get(
        route('locales.index', {
            word: searchWord.value,
            zh: searchZH.value,
            en: searchEN.value,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = () => {
    debouncedSearch();
};

const handleReset = () => {
    searchWord.value = '';
    searchZH.value = '';
    searchEN.value = '';
    status.value = '';
    form.get(route('locales.index'));
};

const handleView = (locale: Locale) => {
    form.get(route('locales.show', locale.id));
};

const handleEdit = (locale: Locale) => {
    form.get(route('locales.edit', locale.id));
};

const handleToggleStatus = (data: { row: Locale; newStatus: number }) => {
    const { row: locale, newStatus } = data;
    updateStatus(locale, newStatus);
};

const handleDelete = (locale: Locale) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Are you sure you want to delete this locale? This action cannot be undone.`,
        },
        submitOptions: {
            method: 'delete',
            url: route('locales.destroy', locale.id),
            transform: (data) => ({
                ...data,
            }),
            successMessage: `Selection deleted successfully.`,
            errorMessage: `Failed to delete locale. Please try again.`,
            entityName: 'locale',
        },
    });
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';
    form.get(
        route('locales.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const updateStatus = (locale: Locale, newStatus: number) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Do you want to change the status of ${locale.word} to ${newStatus === 0 ? 'Active' : 'Inactive'}?`,
        },
        submitOptions: {
            method: 'put',
            url: route('locales.update-status', locale.id),
            transform: (data) => ({
                ...data,
                status: newStatus,
            }),
            successMessage: `Status of ${locale.word} has been updated.`,
            errorMessage: `Unable to update status for ${locale.word}. Please try again.`,
            entityName: 'locale',
        },
    });
};

const columns = [
    { field: 'word', label: 'Word', sortable: true },
    { field: 'en', label: 'English Translation', sortable: true },
    { field: 'zh', label: 'Chinese Translation', sortable: true },
    { field: 'updated_at', label: 'Updated At', sortable: true, width: 'w-40', format: (value) => formatDateTime(value) },
    { field: 'updated_by.name', label: 'Updated By', sortable: true },
    { field: 'status', label: 'Status', sortable: true, width: 'w-30' },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Locales" />

        <div class="px-4 py-3">
            <Heading title="Locales" pageNumber="P000026" />

            <SearchCard v-model:searchValue="searchWord" searchLabel="Word" searchPlaceholder="Word" @search="handleSearch" @reset="handleReset">
                <template #additional-filters>
                    <div>
                        <Label class="pb-2" for="search-input">English Translation</Label>
                        <Input placeholder="English" v-model="searchEN" />
                    </div>
                    <div>
                        <Label class="pb-2" for="search-input">Chinese Translation</Label>
                        <Input placeholder="Chinese" v-model="searchZH" />
                    </div>
                </template>
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('locales.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Locale
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="locales.data"
                        :sort-state="sortState"
                        empty-message="No locale found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        @delete="handleDelete"
                        @toggleStatus="handleToggleStatus"
                        :showDeleteButton="true"
                        :showStatusToggle="true"
                        :showViewButton="true"
                    >
                        <!-- Custom cell renderers -->
                        <template #cell-status="{ row }">
                            <Badge
                                :class="[
                                    {
                                        'bg-green': row.status === 0,
                                        'bg-canary': row.status === 1,
                                    },
                                    'text-md w-15 px-1 py-0',
                                ]"
                            >
                                {{ row.status === 0 ? 'Active' : 'Inactive' }}
                            </Badge>
                        </template>
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="locales.from" :to="locales.to" :total="locales.total" entityName="locale" />
                            </div>
                            <Pagination :links="locales.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
