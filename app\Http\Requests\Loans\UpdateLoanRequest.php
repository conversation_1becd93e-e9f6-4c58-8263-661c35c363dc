<?php

namespace App\Http\Requests\Loans;

use App\Enums\Loan\LoanStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateLoanRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $isDraft = $this->boolean('_saveAsDraft');
        $requiredOrNullable = $isDraft ? 'nullable' : 'required';

        $rules = [
            'borrower_id' => ['required', 'integer', 'exists:loan_customer_profiles,id'],
            'company_id' => ['required', 'integer', 'exists:companies,id'],
            'team_id' => ['required', 'integer', 'exists:teams,id'],
            'agent_id' => ['required', 'integer', 'exists:users,id'],
            'selection_type_id' => ['required', 'integer', 'exists:selections,id'],
            'status' => ['required', new Enum(LoanStatus::class)],
        ];

        if ((int) $this->input('status') === 2) {

            if ((int) $this->input('selection_type_id') === 382) {
                $rules += [
                    'collateral' => ['required', 'array'],
                    'collateral.*.id' => ['required', 'integer', 'exists:collaterals,id'],
                    'collateral.*._delete' => ['required', 'boolean'],
                ];
            } else {
                $rules += [
                    'collateral' => ['nullable', 'array'],
                    'collateral.*.id' => ['nullable', 'integer', 'exists:collaterals,id'],
                    'collateral.*._delete' => ['nullable', 'boolean'],
                ];
            }

            $rules += [
                'loan.selection_mode_type_id' => ['required', 'integer', 'exists:selections,id'],
                'loan.loan_principle_amount' => ['required', 'numeric', 'min:0'],
                'loan.no_of_instalment' => ['required', 'numeric', 'min:0'],
                'loan.instalment_amount' => ['required', 'numeric', 'min:0'],
                'loan.selection_repayment_method_id' => ['required', 'integer', 'exists:selections,id'],
                'loan.late_payment_charges' => ['required', 'numeric', 'min:0'],
                'loan.interest' => ['required', 'numeric', 'min:0'],
                'loan.last_payment' => ['required', 'numeric', 'min:0'],

                'document' => ['nullable', 'array'],
                'document.*.id' => [
                    'nullable',
                    'integer',
                    'min:1',
                    'exists:documents,id',
                    'required_without:document.*.doc_id', // Require id if doc_id is not present
                ],
                'document.*.doc_id' => [
                    'nullable',
                    'integer',
                    'min:1',
                    'exists:documents,id', // Adjust column if doc_id is different
                    'required_without:document.*.id', // Require doc_id if id is not present
                ],
                'document.*._delete' => ['nullable', 'boolean'],
            ];

        } elseif ((int) $this->input('status') === 7) {
            $rules += [
                'loan.commencement_date' => ['required', 'date'],
                'loan.next_due_date' => ['required', 'date'],
                'loan.stamping_date' => ['required', 'date'],
                'loan.last_payment' => ['required', 'numeric', 'min:0'],
                // 'loan.rebate' => ['nullable', 'numeric', 'min:0'],
                'loan.stamp_duty' => ['required', 'numeric', 'min:0'],
                'loan.attestation_fee' => ['required', 'numeric', 'min:0'],
                'loan.legal_fee' => ['required', 'numeric', 'min:0'],
                'loan.processing_fee' => ['required', 'numeric', 'min:0'],
                'loan.misc_charges' => ['required', 'numeric', 'min:0'],
            ];
        }

        return $rules;
    }
}
