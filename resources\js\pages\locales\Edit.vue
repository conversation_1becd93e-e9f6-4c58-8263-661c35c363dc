<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const { formatEnumOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();

interface Props {
    locale: {
        id: number;
        uuid: string;
        word: string;
        en: string | null;
        zh: string | null;
        status: number;
    };
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    word: props.locale.word,
    en: props.locale.en || '',
    zh: props.locale.zh || '',
    status: props.locale.status,
    id: props.locale.id,
});

const formFields = computed(() => [
    {
        id: 'word',
        label: 'Word',
        type: 'input',
        required: true,
        placeholder: 'Word',
        error: form.errors.word,
        modelValue: form.word,
        updateValue: (value: string) => (form.word = value),
    },
    {
        id: 'en',
        label: 'English Translation',
        type: 'input',
        required: false,
        placeholder: 'English Translation',
        error: form.errors.en,
        modelValue: form.en,
        updateValue: (value: string) => (form.en = value),
    },
    {
        id: 'zh',
        label: 'Chinese Translation',
        type: 'input',
        required: false,
        placeholder: 'Chinese Translation',
        error: form.errors.zh,
        modelValue: form.zh,
        updateValue: (value: string) => (form.zh = value),
    },
    {
        id: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        placeholder: 'Status',
        error: form.errors.status,
        options: formatEnumOptions(props.statuses),
        modelValue: form.status,
        updateValue: (value: number) => (form.status = value),
    },
]);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('locales.update', props.locale.id),
            entityName: 'locale',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Edit Locale" />

        <div class="px-4 py-3">
            <Heading title="Locale" pageNumber="P000050" description="Edit the selected locale record" />

            <AppCard title="Edit Locale" :form="form" backRoute="locales.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
