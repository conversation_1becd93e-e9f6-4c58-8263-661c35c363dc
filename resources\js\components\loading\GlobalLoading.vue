<script setup lang="ts">
import LoadingOverlay from '@/components/loading/LoadingOverlay.vue';
import { router } from '@inertiajs/vue3';
import { onMounted, ref } from 'vue';

const isLoading = ref(false);

onMounted(() => {
    router.on('start', () => {
        isLoading.value = true;
    });

    router.on('finish', () => {
        isLoading.value = false;
    });
});
</script>

<template>
    <LoadingOverlay :show="isLoading" />
</template>
