<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * CollateralPropertyOwner model for managing additional property owners of collaterals
 */
class CollateralPropertyOwner extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'collateral_property_id',
        'name',
        'identity_no',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'uuid' => 'string',
        'collateral_property_id' => 'integer',
        'deleted_at' => 'datetime',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
    ];

    /**
     * Get the collateral property that owns this property owner.
     */
    public function collateralProperty(): BelongsTo
    {
        return $this->belongsTo(CollateralProperty::class);
    }

    /**
     * Get the address for this property owner.
     */
    public function address(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    /**
     * Get the contacts for this property owner.
     */
    public function contacts(): MorphMany
    {
        return $this->morphMany(Contact::class, 'contactable');
    }

    /**
     * Get the telephone contact for this property owner.
     */
    public function telephone(): ?Contact
    {
        return $this->contacts()->where('category', Contact::CATEGORY_TELEPHONE)->first();
    }

    /**
     * Get the mobile phone contact for this property owner.
     */
    public function mobilePhone(): ?Contact
    {
        return $this->contacts()->where('category', Contact::CATEGORY_MOBILE)->first();
    }

    /**
     * Get the telephone number for this property owner.
     */
    public function getTelephoneAttribute()
    {
        $contact = $this->telephone();

        return $contact ? $contact->contact : null;
    }

    /**
     * Get the mobile phone number for this property owner.
     */
    public function getMobilePhoneAttribute()
    {
        $contact = $this->mobilePhone();

        return $contact ? $contact->contact : null;
    }
}
