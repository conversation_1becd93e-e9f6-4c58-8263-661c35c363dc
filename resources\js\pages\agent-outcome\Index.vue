<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Outcome } from '@/types';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Props {
    agentOutcome: PaginatedData<Outcome>;
    filters: FilterOptions & {
        agent_name?: string;
        outcome_type?: string;
    };
}

const props = defineProps<Props>();
const form = useForm({});
const searchAgent = ref(props.filters.agent_name || '');
const searchOutcome = ref(props.filters.outcome_type || '');

const debouncedSearch = useDebounceFn(() => {
    form.get(
        route('agent-outcomes.index', {
            agent_name: searchAgent.value,
            outcome_type: searchOutcome.value,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = () => {
    debouncedSearch();
};

const handleReset = () => {
    searchAgent.value = '';
    searchOutcome.value = '';
    form.get(route('agent-outcomes.index'));
};

const handleView = (outcome: Outcome) => {
    form.get(route('agent-outcomes.show', outcome.id));
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';

    form.get(
        route('agent-outcomes.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const handleDelete = (outcome: Outcome) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Are you sure you want to delete this agent outcome? This action cannot be undone.`,
        },
        submitOptions: {
            method: 'delete',
            url: route('agent-outcomes.destroy', outcome.id),
            transform: (data) => ({
                ...data,
            }),
            successMessage: `Agent outcome deleted successfully.`,
            errorMessage: `Failed to delete agent outcome. Please try again.`,
            entityName: 'agent outcome',
        },
    });
};

const columns = [
    { field: 'agent.display_name', label: 'Agent Name', sortable: true, width: 'w-40' },
    { field: 'company.display_name', label: 'Company Name', sortable: true },
    { field: 'outcomeType', label: 'Outcome Type', sortable: true },
    { field: 'amount', label: 'Amount', sortable: true },
    { field: 'remark', label: 'Remark', sortable: true },
    { field: 'updated_at', label: 'Updated At', sortable: true, width: 'w-40', format: (value) => formatDateTime(value) },
    { field: 'updated_by.name', label: 'Updated By', sortable: true },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Agent Outcome" />

        <div class="px-4 py-3">
            <Heading title="Agent Outcome" pageNumber="P000029" />

            <SearchCard
                v-model:searchValue="searchAgent"
                searchLabel="Agent Name"
                searchPlaceholder="Agent Name"
                @search="handleSearch"
                @reset="handleReset"
            >
                <template #additional-filters>
                    <div>
                        <Label class="pb-2" for="search-input">Outcome Type</Label>
                        <Input placeholder="Outcome Type" v-model="searchOutcome" />
                    </div>
                </template>
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('agent-outcomes.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Agent Outcome
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="agentOutcome.data"
                        :sort-state="sortState"
                        empty-message="No agent outcome found."
                        @sort="handleSort"
                        @view="handleView"
                        @delete="handleDelete"
                        :showEditButton="false"
                        :showDeleteButton="true"
                        :showStatusToggle="false"
                    >
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries
                                    :from="agentOutcome.from"
                                    :to="agentOutcome.to"
                                    :total="agentOutcome.total"
                                    entityName="agentOutcome"
                                />
                            </div>
                            <Pagination :links="agentOutcome.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
