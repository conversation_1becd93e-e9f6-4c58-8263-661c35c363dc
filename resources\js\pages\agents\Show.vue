<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/composables/useAuth';
import AppLayout from '@/layouts/AppLayout.vue';
import { Agent } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';

const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess } = useAuth();

interface Props {
    agent: Agent;
}

const props = defineProps<Props>();
const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Agent: ${props.agent.name}`" />
        <div class="px-4 py-3">
            <Heading title="Agent" pageNumber="P000028" description="View details of the agent record" />

            <AppCard title="View Agent" backRoute="agents.index" :form="form" :itemId="props.agent.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3" v-if="!hasHeadquarterAccess">
                        <Label class="font-medium">Headquarter Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.agent.headquarter?.display_name }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3" v-if="!hasCompanyAccess && !isHeadquarter">
                        <Label class="font-medium">Company Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.agent.company?.display_name || '-' }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Agent Code</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.agent.code }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Agent Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.agent.name }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Email</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.agent.email || '-' }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Status</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.agent.status === 0 ? 'Active' : 'Inactive' }}
                        </dd>
                    </div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
