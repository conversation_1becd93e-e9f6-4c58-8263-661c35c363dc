<?php

namespace App\Policies\Agents;

use App\Enums\AccessControl\PermissionName;
use App\Enums\AccessControl\RoleName;
use App\Models\AgentOutcomeType;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AgentOutcomeTypePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any agent outcome types.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_AGENT_OUTCOME_TYPES->value);
    }

    /**
     * Determine whether the user can view the agent outcome type.
     *
     * @return bool
     */
    public function view(User $user, AgentOutcomeType $agentOutcomeType)
    {
        return $user->hasPermissionTo(PermissionName::READ_AGENT_OUTCOME_TYPES->value);
    }

    /**
     * Determine whether the user can create agent outcome types.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_AGENT_OUTCOME_TYPES->value);
    }

    /**
     * Determine whether the user can update the agent outcome type.
     *
     * @return bool
     */
    public function update(User $user, AgentOutcomeType $agentOutcomeType)
    {
        if (! $user->hasCompanyAccess($agentOutcomeType->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_AGENT_OUTCOME_TYPES->value);
    }

    /**
     * Determine whether the user can delete the agent outcome type.
     *
     * @return bool
     */
    public function delete(User $user, AgentOutcomeType $agentOutcomeType)
    {
        if (! $user->hasCompanyAccess($agentOutcomeType->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::DELETE_AGENT_OUTCOME_TYPES->value);
    }

    /**
     * Determine whether the user can restore the agent outcome type.
     *
     * @return bool
     */
    public function restore(User $user, AgentOutcomeType $agentOutcomeType)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_AGENT_OUTCOME_TYPES->value);
    }

    /**
     * Determine whether the user can permanently delete the agent outcome type.
     *
     * @return bool
     */
    public function forceDelete(User $user, AgentOutcomeType $agentOutcomeType)
    {
        // Only Super Administrator can force delete agent outcome types
        return $user->hasRole(RoleName::SUPER_ADMIN->value);
    }
}
