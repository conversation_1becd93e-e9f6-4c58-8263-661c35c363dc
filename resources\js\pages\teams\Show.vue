<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/composables/useAuth';
import AppLayout from '@/layouts/AppLayout.vue';
import { Team } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';

const { hasHeadquarterAccess, hasCompanyAccess } = useAuth();

interface Props {
    team: Team;
}

const props = defineProps<Props>();
const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Team: ${props.team.name}`" />
        <div class="px-4 py-3">
            <Heading title="Team" pageNumber="P000012" description="View details of the team record" />

            <AppCard title="View Team" backRoute="teams.index" :form="form" :itemId="props.team.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Team Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.team.name }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Team Code</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.team.code }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Contact No</Label>
                        <dd class="col-span-2 text-right text-sm">
                            ({{ props.team.contact?.selection_country_id }}) {{ props.team.contact?.contact }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3" v-if="!hasHeadquarterAccess">
                        <Label class="font-medium">Headquarter Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.headquarter !== null ? props.team.headquarter.display_name : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3" v-if="!hasCompanyAccess">
                        <Label class="font-medium">Company Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.company !== null ? props.team.company.display_name : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Email Address</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.email !== null ? props.team.email : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Website Address</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.website !== null ? props.team.website : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Address</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.address?.line_1 || '-' }}{{ props.team.address?.line_2 ? ', ' + props.team.address.line_2 : '' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Postcode</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.address?.postcode !== null ? props.team.address?.postcode : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">City</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.address?.city ? props.team.address.city : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">State</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.address?.selection_state_id ? props.team.address.selection_state_id : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Country</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.address?.selection_country_id ? props.team.address.selection_country_id : '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Status</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.team.status === 0 ? 'Active' : 'Inactive' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3"></div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
