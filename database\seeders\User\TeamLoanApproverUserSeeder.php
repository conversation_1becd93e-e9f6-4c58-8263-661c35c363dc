<?php

namespace Database\Seeders\User;

use App\Enums\User\UserStatus;
use App\Models\AdminProfile;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class TeamLoanApproverUserSeeder extends Seeder
{
    public function run(): void
    {
        $team = Team::firstWhere('name', 'Company Team 2');
        if (! $team) {
            $this->command->error('Team not found. Please run TeamSeeder first.');

            return;
        }

        $teamLoanApproverRole = Role::firstWhere('name', 'Team Loan Approver');
        if (! $teamLoanApproverRole) {
            $this->command->error('Team Loan Approver role not found. Please run RoleSeeder first.');

            return;
        }

        $teamLoanApprover = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'TeamLoanApprover',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'remember_token' => Str::random(10),
                'status' => UserStatus::ACTIVE,
            ]
        );

        if (! $teamLoanApprover->hasRole($teamLoanApproverRole)) {
            $teamLoanApprover->assignRole($teamLoanApproverRole);
        }

        if (! $teamLoanApprover->adminProfiles()->exists()) {
            $superAdminId = User::where('email', '<EMAIL>')->value('id');

            $adminProfile = AdminProfile::create([
                'created_by' => $superAdminId,
                'updated_by' => $superAdminId,
            ]);

            $adminProfile->users()->attach($teamLoanApprover->id);

            $adminProfile->teams()->syncWithoutDetaching([$team->id]);
        }

        $this->command->info('Team loan approver user created successfully.');
    }
}
