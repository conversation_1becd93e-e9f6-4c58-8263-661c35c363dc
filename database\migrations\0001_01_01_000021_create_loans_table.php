<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loans', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('code', 36)->unique();

            // Foreign key relationships
            $table->foreignId('company_id')->constrained('companies')->cascadeOnDelete();
            $table->string('company')->nullable();
            $table->foreignId('team_id')->constrained('teams')->cascadeOnDelete();
            $table->string('team')->nullable();
            $table->foreignId('agent_id')->constrained('users')->cascadeOnDelete();
            $table->string('agent')->nullable();

            // Selection references
            $table->foreignId('selection_type_id')->constrained('selections')->cascadeOnDelete();
            $table->string('type')->nullable();

            // Loan information
            $table->unsignedTinyInteger('status');
            $table->text('remark')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loans');
    }
};
