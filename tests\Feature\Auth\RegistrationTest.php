<?php

// test('registration screen can be rendered', function () {
//     $response = $this->get('/register');

//     $response->assertStatus(200);
// });

// test('new users can register with email', function () {
//     $response = $this->post('/register', [
//         'username' => 'testuser',
//         'email' => '<EMAIL>',
//         'password' => 'password',
//         'password_confirmation' => 'password',
//     ]);

//     $this->assertAuthenticated();
//     $response->assertRedirect(route('dashboard', absolute: false));
// });

// test('new users can register without email', function () {
//     $response = $this->post('/register', [
//         'username' => 'testuser2',
//         'password' => 'password',
//         'password_confirmation' => 'password',
//     ]);

//     $this->assertAuthenticated();
//     $response->assertRedirect(route('dashboard', absolute: false));
// });
