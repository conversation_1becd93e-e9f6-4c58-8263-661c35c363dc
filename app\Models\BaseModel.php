<?php

namespace App\Models;

use App\Traits\HasAuditFields;
use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

/**
 * Base model class with common functionality
 *
 * This class provides common functionality for all models in the application,
 * including UUID generation, audit fields, and soft deletes.
 */
abstract class BaseModel extends Model implements Auditable
{
    use HasAuditFields, HasUuid;
    use \OwenIt\Auditing\Auditable;
    use SoftDeletes;
}
