<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * CustomerEmployment model for managing customer employment information
 */
class CustomerEmployment extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'customer_id',
        'employer_name',
        'length_service_year',
        'length_service_month',
        'job_position',
        'selection_terms_of_employment_id',
        'terms_of_employment',
        'selection_occupation_id',
        'occupation',
        'selection_business_category_id',
        'business_category',
        'gross_income',
        'net_income',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'customer_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the customer that owns the contact.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(CustomerProfile::class, 'customer_id');
    }

    /**
     * Get the selection for terms of employment.
     */
    public function selectionTermsOfEmployment(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_terms_of_employment_id');
    }

    /**
     * Get the selection for occupation.
     */
    public function selectionOccupation(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_occupation_id');
    }

    /**
     * Get the selection for business category.
     */
    public function selectionBusinessCategory(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_business_category_id');
    }

    /**
     * Get the address for the customer employment.
     */
    public function address(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    /**
     * Get the contacts for the customer employment.
     */
    public function contacts(): MorphMany
    {
        return $this->morphMany(Contact::class, 'contactable');
    }

    /**
     * Get the telephone contact for the customer employment.
     */
    public function telephone(): ?Contact
    {
        return $this->contacts()->where('category', Contact::CATEGORY_TELEPHONE)->first();
    }

    /**
     * Get the mobile phone contact for this property owner.
     */
    public function mobilePhone(): ?Contact
    {
        return $this->contacts()->where('category', Contact::CATEGORY_MOBILE)->first();
    }

    /**
     * Get the telephone number for this property owner.
     */
    public function getTelephoneAttribute()
    {
        $contact = $this->telephone();

        return $contact ? $contact->contact : null;
    }

    /**
     * Get the mobile phone number for this property owner.
     */
    public function getMobilePhoneAttribute()
    {
        $contact = $this->mobilePhone();

        return $contact ? $contact->contact : null;
    }
}
