<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

const props = defineProps<{
    locale: {
        id: number;
        uuid: string;
        word: string;
        en: string | null;
        zh: string | null;
        status: number;
        created_at: string;
        updated_at: string;
        created_by: {
            id: number;
            name: string;
        } | null;
        updated_by: {
            id: number;
            name: string;
        } | null;
    };
}>();

const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Locale - ${locale.value}`" />

        <div class="px-4 py-3">
            <Heading title="Locale" pageNumber="P000028" description="View details of the locale record" />

            <AppCard title="View Locale" backRoute="locales.index" :form="form" :itemId="props.locale.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Word</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.locale.word }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">English Translation</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.locale.en || '-' }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Chinese Translation</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.locale.zh }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Status</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.locale.status === 0 ? 'Active' : 'Inactive' }}
                        </dd>
                    </div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
