<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * LoanEmergencyDetail model for managing loan emergency detail information
 */
class LoanEmergencyDetail extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'loan_emergency_id',
        'type',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_emergency_id' => 'integer',
            'type' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan emergency that owns this detail.
     */
    public function loanEmergency(): BelongsTo
    {
        return $this->belongsTo(LoanEmergency::class, 'loan_emergency_id');
    }

    /**
     * Get all of the emergency detail's address.
     */
    public function address(): MorphMany
    {
        return $this->morphMany(Address::class, 'addressable');
    }

    /**
     * Get all of the emergency detail's contact.
     */
    public function contact(): MorphMany
    {
        return $this->morphMany(Contact::class, 'contactable');
    }
}
