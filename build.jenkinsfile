@Library('library@0.0.1.X') _

properties([
    parameters(
        projectEnvironmentParameter() +
        common.awsRegionParameter() +
        appNameParameter() +
        common.versionParameter() +
        common.deployProjectParameter()
    ) // end of parameters
]) // end of properties

projectPipeline = [
    environment: [
        PROJECT_CODE: "LAS",

        AWS_CLI_BUILDER_CREDENTIALS_ID_JSON: '''{
            "DEV":"LAS-DEV-BUILDER",
            "QA":"LAS-DEV-BUILDER"
        }''',

        REMOTE_DOCKER_REGISTRY_USER: "AWS",

        REMOTE_DOCKER_REGISTRY_URI_JSON: '''{
            "DEV":"711387127071.dkr.ecr.ap-southeast-1.amazonaws.com",
            "QA":"711387127071.dkr.ecr.ap-southeast-1.amazonaws.com"
        }'''
    ], // end of environment map
    stages: [
        build: {projectBuildStage()},
        postBuild: {projectPostBuildStage()}
    ] // end of stage map
]

beLaravelBuildPipelineTemplate(projectPipeline)

def projectBuildStage() {
    log.debug "projectBuildStage() is called"

    env.DOCKER_IMAGE_NAME = "$PROJECT_CODE_IN_LOWER_CASE-$ENVIRONMENT_IN_LOWER_CASE" // las-dev
    log.debug "DOCKER_IMAGE_NAME: $DOCKER_IMAGE_NAME"

    env.APP_NAME = common.cleanUpString("$params.APP_NAME") // LAS DEV
    common.verifyRequiredVariable("APP_NAME",APP_NAME)

    env.DOCKER_IMAGE_TAGGING = VERSION // *******-SNAPSHOT
    log.debug "DOCKER_IMAGE_TAGGING: $DOCKER_IMAGE_TAGGING"

    // always Docker login to remote Docker registry
    // build Docker image (las:*******-SNAPSHOT) and push to remote Docker registry
    log.debug "Docker login to remote Docker registry $REMOTE_DOCKER_REGISTRY_URI"

    withCredentials([[
        $class: 'AmazonWebServicesCredentialsBinding',
        credentialsId: AWS_CLI_BUILDER_CREDENTIALS_ID
    ]]) {
        sh """
            aws ecr get-login-password --region $AWS_REGION | docker login --username $REMOTE_DOCKER_REGISTRY_USER --password-stdin $REMOTE_DOCKER_REGISTRY_URI

            docker buildx build --build-arg APP_NAME="$APP_NAME" --tag $REMOTE_DOCKER_REGISTRY_URI/$DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAGGING --push .
        """
    } // end of aws credentials
} // end of projectBuildStage

def projectPostBuildStage() {
    log.debug "projectPostBuildStage() is called"

    if ("${DEPLOY_PROJECT}" == "YES") {
        build job: "LAS-DEPLOYER/${common.constructEncodedBranchName(env.BRANCH_NAME)}",
        parameters: [
            string(name: 'ENVIRONMENT', value: "${ENVIRONMENT}"),
            string(name: 'AWS_REGION', value: "${AWS_REGION}"),
            string(name: 'VERSION', value: "${VERSION}")
        ].findAll { it != null } // Remove null values from the list
    } // end of checking DEPLOY_PROJECT
} // end of projectPostBuildStage

List projectEnvironmentParameter() {
    return [
        [
            $class      : 'ChoiceParameter',
            choiceType  : 'PT_SINGLE_SELECT',
            description : 'Select the Environment from the Dropdown List',
            filterLength: 1,
            filterable  : false,
            name        : 'ENVIRONMENT',
            script      : [
                $class        : 'GroovyScript',
                fallbackScript: [
                    classpath : [],
                    sandbox   : true,
                    script    : "return['Could not get the projectEnvironmentParameter']"
                ],
                script       : [
                    classpath: [],
                    sandbox  : true,
                    script   : "return['DEV','QA']"
                ]
            ]
        ]
    ]
}

List appNameParameter() {
    return [
        [
            $class              : 'DynamicReferenceParameter',
            choiceType          : 'ET_FORMATTED_HTML',
            description         : 'Application Name',
            name                : 'APP_NAME',
            referencedParameters: 'ENVIRONMENT',
            script              : [
                $class        : 'GroovyScript',
                fallbackScript: [
                    classpath: [],
                    sandbox  : true,
                    script   : "return['Could not get the appNameParameter']"
                ],
                script        : [
                    classpath: [],
                    sandbox  : true,
                    script   : """
                    if (ENVIRONMENT.equals("DEV")) {
                        return "<input type='text' name='value' value='LAS DEV'>"
                    } else if (ENVIRONMENT.equals("QA")) {
                        return "<input type='text' name='value' value='LAS QA'>"
                    }
                    """
                ]
            ]
        ]
    ]
}
