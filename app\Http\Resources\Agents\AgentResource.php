<?php

namespace App\Http\Resources\Agents;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AgentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $hierarchyData = $this->getResolveHierarchy();

        $headquarter = $hierarchyData['headquarter'];
        $company = $hierarchyData['company'];

        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $this->code,
            'name' => $this->name,
            'display_name' => $this->display_name,
            'email' => $this->email,
            'status' => $this->status->value,
            'headquarter' => $headquarter ? [
                'id' => $headquarter->id,
                'display_name' => $headquarter->display_name,
                'code' => $headquarter->code,
            ] : null,
            'company' => $company ? [
                'id' => $company->id,
                'display_name' => $company->is_headquarter ? null : $company->display_name,
                'code' => $company->code,
            ] : null,
            'remark' => $this->remark,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];
    }
}
