<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { ref, watch } from 'vue';

interface Props {
    id: string;
    label: string;
    modelValue: string;
    placeholder?: string;
    required?: boolean;
    maxlength?: string | number;
    error?: string;
    class?: string;
    labelClass?: string;
    inputClass?: string;
    allowZero?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '',
    required: false,
    maxlength: 255,
    class: 'gap-2',
    labelClass: '',
    inputClass: '',
    allowZero: true,
});

const emit = defineEmits(['update:modelValue']);

const currencyValue = ref(props.modelValue ?? '');

const formatDecimal = (value: string | number) => {
    if (value === null || value === undefined || value === '') return '';

    let currency = String(value || '')
        .replace(/[^\d.]/g, '') // Remove non-digit/non-dot
        .replace(/(\..*)\./g, '$1') // Allow only one dot
        .replace(/^(\d+)(\.\d{0,2})?.*$/, '$1$2'); // Keep only 2 decimal place
    if (props.allowZero == false) {
        currency = String(currency || '').replace(/^0(?!\.)/, ''); // Remove leading zeros
    }
    return currency;
};

const formatUSCurrency = (value: string | number) => {
    const formatCurrency = formatDecimal(value);
    return formatCurrency.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

watch(
    () => props.modelValue,
    (val) => {
        const currencyData = formatUSCurrency(val);
        currencyValue.value = currencyData;
    },
    { immediate: true },
);

const onInputRaw = (e: Event) => {
    const target = e.target as HTMLInputElement;
    const val = target.value;

    //Get raw clean number (without formatting)
    const cleaned = formatDecimal(val);

    //Format US Currency for display (add commas)
    const formatted = formatUSCurrency(cleaned);

    // Update input and reactive value
    target.value = formatted;

    // Emit raw (unformatted) value to parent
    emit('update:modelValue', cleaned);
};
</script>

<template>
    <div :class="props.class">
        <Label :for="props.id" :class="props.labelClass">
            {{ props.label }}
            <RequiredIndicator v-if="props.required" />
        </Label>
        <Input
            :id="props.id"
            :model-value="currencyValue"
            @input="onInputRaw"
            :required="props.required"
            :maxlength="props.maxlength"
            :placeholder="props.placeholder"
            :class="cn('mt-1', props.inputClass)"
        />
        <InputError class="mt-1" :message="props.error" />
    </div>
</template>
