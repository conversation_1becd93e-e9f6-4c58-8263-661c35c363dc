<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * LoanCustomerProfile model for managing loan customer profile information
 */
class LoanCustomerProfile extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'loan_id',
        'customer_id',
        'code',
        'name',
        'email',
        'identity_no',
        'old_identity_no',
        'registration_date',
        'years_of_incorporation',
        'age',
        'birth_date',
        'is_primary',
        'selection_type_id',
        'type',
        'selection_gender_id',
        'gender',
        'selection_race_id',
        'race',
        'selection_nationality_id',
        'nationality',
        'selection_education_level_id',
        'education_level',
        'selection_marriage_status_id',
        'marriage_status',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_id' => 'integer',
            'customer_id' => 'integer',
            'age' => 'integer',
            'years_of_incorporation' => 'integer',
            'registration_date' => 'date',
            'birth_date' => 'date',
            'is_primary' => 'boolean',
            'selection_type_id' => 'integer',
            'selection_gender_id' => 'integer',
            'selection_race_id' => 'integer',
            'selection_nationality_id' => 'integer',
            'selection_education_level_id' => 'integer',
            'selection_marriage_status_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan that owns this customer profile.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class, 'loan_id');
    }

    /**
     * Get the customer profile associated with this loan customer profile.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(CustomerProfile::class, 'customer_id');
    }

    /**
     * Get the loan customer addresses for this profile.
     */
    public function loanCustomerAddresses(): HasMany
    {
        return $this->hasMany(LoanCustomerAddress::class, 'loan_customer_profile_id');
    }

    /**
     * Get the loan customer contacts for this profile.
     */
    public function loanCustomerContacts(): HasMany
    {
        return $this->hasMany(LoanCustomerContact::class, 'loan_customer_profile_id');
    }

    /**
     * Get the loan customer employment for this profile.
     */
    public function loanCustomerEmployment(): HasOne
    {
        return $this->hasOne(LoanCustomerEmployment::class, 'loan_customer_profile_id');
    }

    /**
     * Get the loan customer company for this profile.
     */
    public function loanCustomerCompany(): HasOne
    {
        return $this->hasOne(LoanCustomerCompany::class, 'loan_customer_profile_id');
    }

    /**
     * Get the loan customer collaterals for this profile.
     */
    public function loanCustomerCollaterals(): HasMany
    {
        return $this->hasMany(LoanCustomerCollateral::class, 'loan_customer_profile_id');
    }

    /**
     * Get the selection type associated with this loan customer profile.
     */
    public function selectionType(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_type_id');
    }

    /**
     * Get the selection gender associated with this loan customer profile.
     */
    public function selectionGender(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_gender_id');
    }

    /**
     * Get the selection race associated with this loan customer profile.
     */
    public function selectionRace(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_race_id');
    }

    /**
     * Get the selection nationality associated with this loan customer profile.
     */
    public function selectionNationality(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_nationality_id');
    }

    /**
     * Get the selection education level associated with this loan customer profile.
     */
    public function selectionEducationLevel(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_education_level_id');
    }

    /**
     * Get the selection marriage status associated with this loan customer profile.
     */
    public function selectionMarriageStatus(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_marriage_status_id');
    }
}
