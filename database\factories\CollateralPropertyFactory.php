<?php

namespace Database\Factories;

use App\Models\Collateral;
use App\Models\CollateralProperty;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CollateralProperty>
 */
class CollateralPropertyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CollateralProperty::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'collateral_id' => Collateral::factory(),
            'ownership_no' => fake()->unique()->numerify('OWN-######'),
            'lot_number' => fake()->numerify('LOT-####'),
            'selection_land_category_id' => null,
            'land_category' => fake()->randomElement(['Residential', 'Commercial', 'Industrial', 'Agricultural', 'Other']),
            'land_category_other' => null,
            'type_of_property' => fake()->randomElement(['House', 'Apartment', 'Land', 'Office', 'Shop']),
            'land_size' => fake()->numerify('#### sq ft'),
            'selection_land_status_id' => null,
            'land_status' => fake()->randomElement(['Freehold', 'Leasehold', 'Other']),
            'no_syit_piawai' => fake()->numerify('SP-####'),
            'certified_plan_no' => fake()->numerify('CP-####'),
            'built_up_area_of_property' => fake()->numerify('#### sq ft'),
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Configure the model factory to associate with an existing collateral.
     */
    public function forCollateral(Collateral $collateral): static
    {
        return $this->state(fn (array $attributes) => [
            'collateral_id' => $collateral->id,
        ]);
    }
}
