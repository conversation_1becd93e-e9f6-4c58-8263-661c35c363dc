<script setup lang="ts">
import FormInputSearch from '@/components/form/FormInputSearch.vue';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

const form = useForm({
    search: null as string | null,
});

const keywords = ['name', 'ic'];
const icons = ['circle-user', 'id-card'];
const defaultSearchValue = 'ic';

const handleResults = (newSearch: string) => {
    form.search = newSearch;
};
</script>

<template>
    <AppLayout>
        <Head title="Search Input Examples" />

        <div class="px-4 py-6">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- User Search -->
                <Card>
                    <CardHeader>
                        <CardTitle>User Search</CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div>
                            <Label for="user-search">Search Users</Label>
                            <FormInputSearch
                                api="/api/search-api"
                                @resultsUpdated="handleResults"
                                :labelKeywords="keywords"
                                iconPosition="right"
                                :defaultSearchValue="defaultSearchValue"
                                :icons="icons"
                            ></FormInputSearch>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    </AppLayout>
</template>
