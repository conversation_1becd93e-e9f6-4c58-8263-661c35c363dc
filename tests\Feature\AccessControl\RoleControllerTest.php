<?php

use <PERSON><PERSON>\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

test('guests cannot access role management', function () {
    $response = $this->get('/roles');
    $response->assertRedirect('/login');
});

test('role index page displays roles list', function () {
    $user = $this->createSuperAdmin();
    $role = Role::create(['name' => 'test-role']);

    $response = $this->actingAs($user)
        ->get('/roles');

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page
        ->component('roles/Index')
        ->has('roles.data', 1)
        ->where('roles.data.0.name', 'test-role')
    );
});

test('role search filters work correctly', function () {
    $user = $this->createSuperAdmin();
    Role::create(['name' => 'test-role']);
    Role::create(['name' => 'another-role']);

    $response = $this->actingAs($user)
        ->get('/roles?name=test');

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page
        ->component('roles/Index')
        ->has('roles.data', 1)
        ->where('roles.data.0.name', 'test-role')
    );
});

test('create role page can be rendered', function () {
    $user = $this->createSuperAdmin();
    Permission::create(['name' => 'test.permission']);

    $response = $this->actingAs($user)
        ->get('/roles/create');

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page
        ->component('roles/Create')
        ->has('permissions', 1)
    );
});

test('role can be created with permissions', function () {
    $user = $this->createSuperAdmin();
    $permission = Permission::create(['name' => 'test.permission']);

    $response = $this->actingAs($user)
        ->post('/roles', [
            'name' => 'test-role',
            'permissions' => [$permission->id],
            'level' => 100,
        ]);

    $response->assertRedirect('/roles');
    $response->assertSessionHas('message', 'Role created successfully');

    $this->assertDatabaseHas('roles', [
        'name' => 'test-role',
    ]);

    $role = Role::where('name', 'test-role')->first();
    expect($role->permissions)->toHaveCount(1);
    expect($role->permissions->first()->name)->toBe('test.permission');
});

test('role cannot be created with duplicate name', function () {
    $user = $this->createSuperAdmin();
    Role::create(['name' => 'test-role']);

    $response = $this->actingAs($user)
        ->post('/roles', [
            'name' => 'test-role',
            'permissions' => [],
        ]);

    $response->assertSessionHasErrors(['name']);
    $this->assertDatabaseCount('roles', 2);
});

test('edit role page can be rendered', function () {
    $user = $this->createSuperAdmin();
    $role = Role::create(['name' => 'test-role']);
    $permission = Permission::create(['name' => 'test.permission']);
    $role->givePermissionTo($permission);

    $response = $this->actingAs($user)
        ->get("/roles/{$role->id}/edit");

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page
        ->component('roles/Edit')
        ->where('role.name', 'test-role')
        ->where('role.permissions', [$permission->id])
        ->has('permissions', 1)
    );
});

test('role can be updated with permissions', function () {
    $user = $this->createSuperAdmin();
    $role = Role::create(['name' => 'test-role']);
    $permission = Permission::create(['name' => 'test.permission']);

    $response = $this->actingAs($user)
        ->put("/roles/{$role->id}", [
            'name' => 'updated-role',
            'permissions' => [$permission->id],
            'level' => 100,
        ]);

    $response->assertRedirect('/roles');
    $response->assertSessionHas('message', 'Role updated successfully');

    $this->assertDatabaseHas('roles', [
        'name' => 'updated-role',
    ]);

    $role = Role::where('name', 'updated-role')->first();
    expect($role->permissions)->toHaveCount(1);
    expect($role->permissions->first()->name)->toBe('test.permission');
});

test('role can be deleted', function () {
    $user = $this->createSuperAdmin();
    $role = Role::create(['name' => 'test-role']);

    $response = $this->actingAs($user)
        ->delete("/roles/{$role->id}");

    $response->assertRedirect('/roles');
    $response->assertSessionHas('message', 'Role deleted successfully');
    $this->assertDatabaseMissing('roles', [
        'name' => 'test-role',
    ]);
});
