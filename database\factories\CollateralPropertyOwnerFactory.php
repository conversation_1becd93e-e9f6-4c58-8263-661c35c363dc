<?php

namespace Database\Factories;

use App\Models\CollateralProperty;
use App\Models\CollateralPropertyOwner;
use App\Models\Contact;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CollateralPropertyOwner>
 */
class CollateralPropertyOwnerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CollateralPropertyOwner::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'collateral_property_id' => CollateralProperty::factory(),
            'name' => fake()->name(),
            'identity_no' => fake()->numerify('##########'),
            'remark' => fake()->optional()->sentence(),
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Configure the factory to create contacts after creating the model.
     */
    public function configure(): static
    {
        return $this->afterCreating(function (CollateralPropertyOwner $owner) {
            // Create telephone contact
            Contact::factory()
                ->telephone()
                ->forModel(CollateralPropertyOwner::class, $owner->id)
                ->create();

            // Create mobile phone contact
            Contact::factory()
                ->mobile()
                ->forModel(CollateralPropertyOwner::class, $owner->id)
                ->create();
        });
    }

    /**
     * Configure the model factory to associate with an existing collateral property.
     */
    public function forCollateralProperty(CollateralProperty $collateralProperty): static
    {
        return $this->state(fn (array $attributes) => [
            'collateral_property_id' => $collateralProperty->id,
        ]);
    }
}
