<?php

use <PERSON><PERSON>\Permission\Models\Permission;

test('guests cannot access permission management', function () {
    $response = $this->get('/permissions');
    $response->assertRedirect('/login');
});

test('permission index page displays permissions list', function () {
    $user = $this->createSuperAdmin();
    $permission = Permission::create(['name' => 'test permission']);

    $response = $this->actingAs($user)
        ->get('/permissions');

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page
        ->component('permissions/Index')
        ->has('permissions.data', 1)
        ->where('permissions.data.0.name', 'test permission')
    );
});

test('permission search filters work correctly', function () {
    $user = $this->createSuperAdmin();
    Permission::create(['name' => 'test permission']);
    Permission::create(['name' => 'another.permission']);

    $response = $this->actingAs($user)
        ->get('/permissions?name=test');

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page
        ->component('permissions/Index')
        ->has('permissions.data', 1)
        ->where('permissions.data.0.name', 'test permission')
    );
});

test('create permission page can be rendered', function () {
    $user = $this->createSuperAdmin();

    $response = $this->actingAs($user)
        ->get('/permissions/create');

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page
        ->component('permissions/Create')
    );
});

test('permission can be created', function () {
    $user = $this->createSuperAdmin();

    $response = $this->actingAs($user)
        ->post('/permissions', [
            'name' => 'test permission',
        ]);

    $response->assertRedirect('/permissions');
    $response->assertSessionHas('message', 'Permission created successfully');
    $this->assertDatabaseHas('permissions', [
        'name' => 'test permission',
    ]);
});

test('permission cannot be created with duplicate name', function () {
    $user = $this->createSuperAdmin();
    Permission::create(['name' => 'test permission']);

    $response = $this->actingAs($user)
        ->post('/permissions', [
            'name' => 'test permission',
        ]);

    $response->assertSessionHasErrors(['name']);
    $this->assertDatabaseCount('permissions', 1);
});

test('edit permission page can be rendered', function () {
    $user = $this->createSuperAdmin();
    $permission = Permission::create(['name' => 'test permission']);

    $response = $this->actingAs($user)
        ->get("/permissions/{$permission->id}/edit");

    $response->assertStatus(200);
    $response->assertInertia(fn ($page) => $page
        ->component('permissions/Edit')
        ->where('permission.name', 'test permission')
    );
});

test('permission can be updated', function () {
    $user = $this->createSuperAdmin();
    $permission = Permission::create(['name' => 'test permission']);

    $response = $this->actingAs($user)
        ->put("/permissions/{$permission->id}", [
            'name' => 'updated permission',
        ]);

    $response->assertRedirect('/permissions');
    $response->assertSessionHas('message', 'Permission updated successfully');
    $this->assertDatabaseHas('permissions', [
        'name' => 'updated permission',
    ]);
});

test('permission can be deleted', function () {
    $user = $this->createSuperAdmin();
    $permission = Permission::create(['name' => 'test permission']);

    $response = $this->actingAs($user)
        ->delete("/permissions/{$permission->id}");

    $response->assertRedirect('/permissions');
    $response->assertSessionHas('message', 'Permission deleted successfully');
    $this->assertDatabaseMissing('permissions', [
        'name' => 'test permission',
    ]);
});
