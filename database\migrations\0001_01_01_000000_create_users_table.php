<?php

use App\Enums\User\UserLocale;
use App\Enums\User\UserStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Authentication and security
            $table->string('username')->unique();
            $table->string('email')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->rememberToken();

            // Status and activity
            $table->unsignedTinyInteger('status')->default(UserStatus::ACTIVE->value);
            $table->timestamp('last_login_time')->nullable();
            $table->string('last_login_ip', 45)->nullable();

            // Localization
            $table->string('locale', 10)->default(UserLocale::ENGLISH->value);

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('userables', function (Blueprint $table) {
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->morphs('userable');
            $table->primary(['user_id', 'userable_id', 'userable_type'], 'userables_primary');
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->nullable();
            $table->string('username')->nullable();
            $table->string('token');
            $table->timestamp('created_at')->nullable();

            // Instead of a primary key, use unique indexes
            $table->unique('email');
            $table->unique('username');
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('userables');
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
