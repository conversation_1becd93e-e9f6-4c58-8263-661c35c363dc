<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoanBankAccount extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'loan_id',
        'selection_bank_id',
        'bank',
        'selection_type_id',
        'type',
        'account_no',
        'account_name',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'loan_id' => 'integer',
            'selection_bank_id' => 'integer',
            'selection_type_id' => 'integer',
            'deleted_at' => 'datetime',
        ];
    }

    /**
     * Get the loan that owns the bank account.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class);
    }

    /**
     * Get the bank selection that owns the bank account.
     */
    public function selectionBank(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_bank_id');
    }

    /**
     * Get the type selection that owns the bank account.
     */
    public function selectionType(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_type_id');
    }
}
