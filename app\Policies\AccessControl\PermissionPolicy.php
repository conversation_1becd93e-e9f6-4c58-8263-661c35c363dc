<?php

namespace App\Policies\AccessControl;

use App\Enums\AccessControl\PermissionName;
use App\Enums\AccessControl\RoleName;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Spatie\Permission\Models\Permission;

class PermissionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any permissions.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_PERMISSIONS->value);
    }

    /**
     * Determine whether the user can view the permission.
     *
     * @return bool
     */
    public function view(User $user, Permission $permission)
    {
        return $user->hasPermissionTo(PermissionName::READ_PERMISSIONS->value);
    }

    /**
     * Determine whether the user can create permissions.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_PERMISSIONS->value);
    }

    /**
     * Determine whether the user can update the permission.
     *
     * @return bool
     */
    public function update(User $user, Permission $permission)
    {
        // Prevent updating critical system permissions
        if (in_array($permission->name, PermissionName::criticalPermissions()) && ! $user->hasRole(RoleName::SUPER_ADMIN)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_PERMISSIONS->value);
    }

    /**
     * Determine whether the user can delete the permission.
     *
     * @return bool
     */
    public function delete(User $user, Permission $permission)
    {
        // Prevent deleting critical system permissions
        if (in_array($permission->name, PermissionName::criticalPermissions())) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::DELETE_PERMISSIONS->value);
    }

    /**
     * Determine whether the user can restore the permission.
     *
     * @return bool
     */
    public function restore(User $user, Permission $permission)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_PERMISSIONS->value);
    }

    /**
     * Determine whether the user can permanently delete the permission.
     *
     * @return bool
     */
    public function forceDelete(User $user, Permission $permission)
    {
        // Only Super Administrator can force delete permissions
        return $user->hasRole(RoleName::SUPER_ADMIN);
    }
}
