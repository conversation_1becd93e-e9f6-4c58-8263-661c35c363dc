<?php

namespace Database\Seeders\Agent;

use App\Models\AgentOutcomeType;
use App\Models\Company;
use Illuminate\Database\Seeder;

class AgentOutcomeTypeSeeder extends Seeder
{
    public function run(): void
    {
        $agentOutcomeTypesByCompany = [
            'HEADQUARTER001' => [
                'HQ Agent Outcome Type 1',
            ],
            'COMPANY001' => [
                'Company Agent Outcome Type 1',
                'Company Agent Outcome Type 2',
                'Company Agent Outcome Type 3',
            ],
        ];

        foreach ($agentOutcomeTypesByCompany as $companyName => $agentOutcomeTypes) {
            $company = Company::firstWhere('name', $companyName);

            if (! $company) {
                $this->command->error("Company '{$companyName}' not found. Please run CompanySeeder first.");

                continue;
            }

            foreach ($agentOutcomeTypes as $agentOutcomeTypeName) {
                if (! AgentOutcomeType::where('name', $agentOutcomeTypeName)->exists()) {
                    $agentOutcomeType = AgentOutcomeType::firstOrCreate(
                        ['name' => $agentOutcomeTypeName],
                        [
                            'company_id' => $company->id,
                            'name' => $agentOutcomeTypeName,
                        ]
                    );

                    $agentOutcomeType->companies()->attach($company->id);
                }
            }
        }

        $this->command->info('Agent outcome types created successfully');
    }
}
