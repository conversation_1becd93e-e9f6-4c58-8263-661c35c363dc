<?php

namespace App\Policies\Collaterals;

use App\Enums\AccessControl\PermissionName;
use App\Models\Collateral;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CollateralPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any collaterals.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_COLLATERALS->value);
    }

    /**
     * Determine whether the user can view the collateral.
     *
     * @return bool
     */
    public function view(User $user, Collateral $collateral)
    {
        return $user->hasPermissionTo(PermissionName::READ_COLLATERALS->value);
    }

    /**
     * Determine whether the user can create collaterals.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_COLLATERALS->value);
    }

    /**
     * Determine whether the user can update the collateral.
     *
     * @return bool
     */
    public function update(User $user, Collateral $collateral)
    {
        if (! $user->hasCompanyAccess($collateral->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_COLLATERALS->value);
    }

    /**
     * Determine whether the user can delete the collateral.
     *
     * @return bool
     */
    public function delete(User $user, Collateral $collateral)
    {
        if (! $user->hasCompanyAccess($collateral->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::DELETE_COLLATERALS->value);
    }

    /**
     * Determine whether the user can restore the collateral.
     *
     * @return bool
     */
    public function restore(User $user, Collateral $collateral)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_COLLATERALS->value);
    }

    /**
     * Determine whether the user can permanently delete the collateral.
     *
     * @return bool
     */
    public function forceDelete(User $user, Collateral $collateral)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_COLLATERALS->value);
    }
}
