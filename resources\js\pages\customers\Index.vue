<script setup lang="ts">
import Calendar from '@/components/calendar/Calendar.vue';
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import FormSelect from '@/components/form/FormSelect.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useFormatOptions } from '@/composables/useFormatOptions';
import AppLayout from '@/layouts/AppLayout.vue';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { formatEnumOptions } = useFormatOptions();

interface Team {
    id: number;
    name: string;
}

interface Customer {
    id: number;
    uuid: string;
    code: string;
    teams: Team[];
    updated_at: string;
    updated_by: {
        id: number;
        name: string;
    } | null;
}

interface Props {
    customers: PaginatedData<Customer>;
    statuses: Record<number, string>;
    filters: FilterOptions & {
        name?: string;
        identity_no?: string;
        contact_no?: string;
        status?: number;
        from_updated_at?: string;
        to_updated_at?: string;
    };
}

const props = defineProps<Props>();

const form = useForm({});

const searchValue = ref(props.filters.name || '');
const identityNo = ref(props.filters.identity_no || '');
const contactNo = ref(props.filters.contact_no || '');
const status = ref(props.filters.status || '');
const fromUpdatedAt = ref(props.filters.from_updated_at || '');
const toUpdatedAt = ref(props.filters.to_updated_at || '');

const debouncedSearch = useDebounceFn((filters: any) => {
    form.get(route('customers.index', filters), {
        preserveState: true,
        preserveScroll: true,
        replace: true,
    });
}, 300);

const handleSearch = () => {
    debouncedSearch({
        name: searchValue.value,
        identity_no: identityNo.value,
        contact_no: contactNo.value,
        status: status.value,
        from_updated_at: fromUpdatedAt.value,
        to_updated_at: toUpdatedAt.value,
    });
};

const handleReset = () => {
    searchValue.value = '';
    identityNo.value = '';
    contactNo.value = '';
    status.value = '';
    fromUpdatedAt.value = '';
    toUpdatedAt.value = '';
    form.get(route('customers.index'));
};

const handleView = (customer: Customer) => {
    form.get(route('customers.show', customer.id));
};

const handleEdit = (customer: Customer) => {
    form.get(route('customers.edit', customer.id));
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';

    form.get(
        route('customers.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const columns = [
    { field: 'code', label: 'Customer No', sortable: true, width: 'w-40' },
    { field: 'display_name', label: 'Name', sortable: true, width: 'w-40' },
    { field: 'identity_no', label: 'IC/Business Registration No.', sortable: true, width: 'w-40' },
    { field: 'contact_no', label: 'Contact No.', sortable: true, width: 'w-40' },
    { field: 'nationality', label: 'Nationality', sortable: true, width: 'w-40' },
    {
        field: 'teams',
        label: 'Team Name',
        sortable: true,
        width: 'w-40',
        format: (value) => value.map((value) => value.name).join(', '),
    },
    {
        field: 'updated_at',
        label: 'Updated At',
        sortable: true,
        width: 'w-40',
        format: (value) => formatDateTime(value),
    },
    { field: 'updated_by.name', label: 'Updated By', sortable: true },
    { field: 'status', label: 'Status', sortable: true, width: 'w-30' },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Customer" />

        <div class="px-4 py-3">
            <Heading title="Customer" pageNumber="P000001" />

            <SearchCard
                v-model:searchValue="searchValue"
                searchLabel="Name"
                searchPlaceholder="Name"
                :status="false"
                @search="handleSearch"
                @reset="handleReset"
            >
                <template #additional-filters>
                    <div class="w-full">
                        <Label class="pb-2" for="customer-ic">IC/Business Registration No.</Label>
                        <Input id="customer-ic" placeholder="IC/Business Registration No." v-model="identityNo" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="customer-contact">Contact No.</Label>
                        <Input id="customer-contact" placeholder="Contact No." v-model="contactNo" class="w-full" />
                    </div>
                    <FormSelect
                        id="status"
                        label="Status"
                        :model-value="status"
                        @update:model-value="status = Number($event)"
                        type="select"
                        :options="formatEnumOptions(props.statuses)"
                        placeholder="Status"
                        :error="form.errors.status"
                        labelClass="pb-1"
                    />
                    <div class="w-full">
                        <Label class="pb-2" for="valuer-received-date-from">Updated At (From)</Label>
                        <Calendar v-model="fromUpdatedAt" placeholderLabel="Updated At (From)" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="valuer-received-date-to">Updated At (To)</Label>
                        <Calendar v-model="toUpdatedAt" placeholderLabel="Updated At (To)" />
                    </div>
                </template>
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('customers.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Customer
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="customers.data"
                        :sort-state="sortState"
                        empty-message="No customers found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        :showDeleteButton="false"
                    >
                        <!-- Custom cell renderers -->
                        <template #cell-status="{ row }">
                            <Badge
                                :class="[
                                    {
                                        'bg-green': row.status === 0,
                                        'bg-canary': row.status === 1,
                                    },
                                    'text-md w-15 px-1 py-0',
                                ]"
                            >
                                {{ row.status === 0 ? 'Active' : 'Inactive' }}
                            </Badge>
                        </template>
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="customers.from" :to="customers.to" :total="customers.total" entityName="customers" />
                            </div>
                            <Pagination :links="customers.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
