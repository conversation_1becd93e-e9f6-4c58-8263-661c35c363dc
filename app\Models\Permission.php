<?php

namespace App\Models;

use App\Traits\HasAuditFields;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Models\Permission as SpatiePermission;

/**
 * Permission model for managing permissions
 */
class Permission extends SpatiePermission
{
    use <PERSON><PERSON><PERSON>tFields, HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'guard_name',
        'created_by',
        'updated_by',
    ];

    /**
     * Get permissions for dropdown selection
     */
    public static function getForDropdown(): array
    {
        return self::all()->map(fn ($permission) => [
            'id' => $permission->id,
            'name' => $permission->name,
        ])->toArray();
    }
}
