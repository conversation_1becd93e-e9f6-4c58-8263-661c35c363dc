<script setup lang="ts">
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@/components/ui/sidebar';
import { formatDateTime } from '@/utils/dateUtils';
import { Plus } from 'lucide-vue-next';
import { type Component, ref } from 'vue';

const props = defineProps<{
    teams: {
        name: string;
        logo: Component;
        version: string;
        lastLoginTime: string;
    }[];
}>();

const { isMobile, state } = useSidebar();
const activeTeam = ref(props.teams[0]);
</script>

<template>
    <SidebarMenu>
        <SidebarMenuItem>
            <DropdownMenu>
                <DropdownMenuTrigger as-child>
                    <SidebarMenuButton :size="state === 'collapsed' ? 'lg' : 'xl'" class="pointer-events-none items-start">
                        <Avatar>
                            <AvatarFallback class="text-sidebar-accent-foreground">
                                <strong>A</strong>
                                <!-- TODO: replace to proper role name -->
                            </AvatarFallback>
                        </Avatar>
                        <div class="grid flex-1 gap-[4px] text-left text-sm leading-tight">
                            <span class="truncate font-bold font-medium">
                                {{ activeTeam.name }}
                            </span>
                            <span class="text-sidebar-foreground/60 truncate text-xs">Version {{ activeTeam.version }}</span>
                            <span class="text-sidebar-foreground/60 truncate text-xs">Last Login Time</span>
                            <span class="truncate text-xs text-sky-500">{{ formatDateTime(activeTeam.lastLoginTime) }}</span>
                        </div>
                    </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                    class="w-[--reka-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                    align="start"
                    :side="isMobile ? 'bottom' : 'right'"
                    :side-offset="4"
                    v-if="teams.length > 1"
                >
                    <DropdownMenuLabel class="text-muted-foreground text-xs"> Teams </DropdownMenuLabel>
                    <DropdownMenuItem v-for="(team, index) in teams" :key="team.name" class="gap-2 p-2" @click="activeTeam = team">
                        <div class="flex size-6 items-center justify-center rounded-sm border">
                            <component :is="team.logo" class="size-3.5 shrink-0" />
                        </div>
                        {{ team.name }}
                        <DropdownMenuShortcut>⌘{{ index + 1 }}</DropdownMenuShortcut>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem class="gap-2 p-2">
                        <div class="flex size-6 items-center justify-center rounded-md border bg-transparent">
                            <Plus class="size-4" />
                        </div>
                        <div class="text-muted-foreground font-medium">Add team</div>
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </SidebarMenuItem>
    </SidebarMenu>
</template>
