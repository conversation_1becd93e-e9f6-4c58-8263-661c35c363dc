<?php

namespace App\Models;

use App\Enums\Loan\LoanTxnType as LoanTxnTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * LoanTxnType model for managing loan transaction types
 */
class LoanTxnType extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'type',
        'sort_order',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'type' => LoanTxnTypeEnum::class,
            'sort_order' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan transactions for this transaction type.
     */
    public function loanTxns(): Has<PERSON>any
    {
        return $this->hasMany(LoanTxn::class);
    }

    /**
     * Get the loan transaction details for this transaction type.
     */
    public function loanTxnDetails(): HasMany
    {
        return $this->hasMany(LoanTxnDetail::class);
    }

    /**
     * Get the display name for the transaction type
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->type->label();
    }

    /**
     * Scope to get installment type
     */
    public function scopeInstallment($query)
    {
        return $query->where('type', LoanTxnTypeEnum::INSTALLMENT);
    }

    /**
     * Scope to get late interest type
     */
    public function scopeLateInterest($query)
    {
        return $query->where('type', LoanTxnTypeEnum::LATE_INTEREST);
    }

    /**
     * Scope to get legal fee type
     */
    public function scopeLegalFee($query)
    {
        return $query->where('type', LoanTxnTypeEnum::LEGAL_FEE);
    }

    /**
     * Scope to get misc charge type
     */
    public function scopeMiscCharge($query)
    {
        return $query->where('type', LoanTxnTypeEnum::MISC_CHARGE);
    }

    /**
     * Scope to get postage type
     */
    public function scopePostage($query)
    {
        return $query->where('type', LoanTxnTypeEnum::POSTAGE);
    }
}
