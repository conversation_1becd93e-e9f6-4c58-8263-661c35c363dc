<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/composables/useAuth';
import AppLayout from '@/layouts/AppLayout.vue';
import { formatDate } from '@/lib/utils';
import { Head } from '@inertiajs/vue3';
import { computed, ref } from 'vue';

const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess, hasTeamAccess } = useAuth();

interface Address {
    id: number;
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    state: string | null;
    selection_state_id: number | null;
    state_selection: string | null;
    country: string | null;
    selection_country_id: number | null;
    country_selection: string | null;
}

interface PropertyOwner {
    id: number;
    name: string;
    identity_no: string;
    telephone_country_selection: string | null;
    mobile_country_selection: string | null;
    telephone: string | null;
    mobile_phone: string | null;
    remark: string | null;
    address: Address | null;
}

interface Valuer {
    id: number;
    valuation_amount: number;
    valuer: string;
    valuation_received_date: string | null;
    land_search_received_date: string | null;
}

interface Property {
    id: number;
    ownership_no: string | null;
    lot_number: string | null;
    selection_land_category_id: number | null;
    land_category: string | null;
    land_category_selection: string | null;
    land_category_other: string | null;
    type_of_property: string | null;
    land_size: string | null;
    selection_land_status_id: number | null;
    land_status: string | null;
    land_status_selection: string | null;
    no_syit_piawai: string | null;
    certified_plan_no: string | null;
    built_up_area_of_property: string | null;
    address: Address | null;
}

interface Collateral {
    id: number;
    uuid: string;
    headquarter_id: number | null;
    headquarter_display_name: string | null;
    company_id: number | null;
    company_display_name: string | null;
    team_id: number | null;
    team_display_name: string | null;
    selection_customer_type_id: number | null;
    customer_type: string | null;
    customer_type_selection: string | null;
    selection_type_id: number | null;
    type: string;
    type_selection: string | null;
    name: string;
    identity_no: string;
    company_name: string | null;
    business_registration_no: string | null;
    valuation_amount: number | null;
    valuer: string | null;
    valuation_received_date: string | null;
    land_search_received_date: string | null;
    status: number;
    status_label: string;
    remark: string | null;
    property: Property | null;
    property_owners: PropertyOwner[];
    valuers: Valuer[];
    created_at: string;
    updated_at: string;
    created_by: {
        id: number;
        username: string;
    } | null;
    updated_by: {
        id: number;
        username: string;
    } | null;
}

interface Props {
    collateral: Collateral;
}

const props = defineProps<Props>();
const activeTab = ref('info');
const openAccordion = ref('0');
const openAccordionIndex = ref<string | undefined>(undefined);
const ordinal = (n) => {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
};

const formatAddress = (address: Address | null) => {
    if (!address) return 'No address provided';

    const line1 = address.line_1;
    const line2 = address.line_2;
    const line3Parts = [address.postcode, address.city, address.state_selection || address.state, address.country_selection || address.country]
        .filter(Boolean)
        .join(', ');

    return [line1, line2, line3Parts].filter(Boolean).join('\n');
};

const tabItems = computed(() => [
    { label: 'Collateral Info', value: 'info' },
    { label: 'Valuation', value: 'valuation' },
    { label: 'Additional Owner', value: 'owner' },
]);
</script>

<template>
    <AppLayout>
        <Head :title="`Collateral: ${collateral.name}`" />

        <div class="px-4 py-6">
            <div class="mb-6 flex items-center justify-between">
                <Heading title="Collateral" pageNumber="P000067" description="View Collateral Details" />
            </div>
            <Card class="gap-0 py-0">
                <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                    <CardTitle>Collateral - {{ collateral.id }}</CardTitle>
                </CardHeader>
                <TabsWrapper v-model="activeTab" :tabs="tabItems">
                    <template #info>
                        <CardContent class="py-4">
                            <Label class="text-[20px]">Collateral Info</Label>
                            <Card class="rounded-xs p-2">
                                <CardContent class="px-1">
                                    <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                        <div v-if="!hasHeadquarterAccess" class="flex items-center justify-between px-1">
                                            <Label for="headquarter-name" class="py-0 text-base">Headquarter Name </Label>
                                            <p>{{ collateral.headquarter_display_name }}</p>
                                        </div>

                                        <div v-if="!hasCompanyAccess" class="flex items-center justify-between px-1">
                                            <Label for="company-name" class="py-0 text-base">Company Name </Label>
                                            <p>{{ collateral.company_display_name }}</p>
                                        </div>

                                        <div v-if="!hasTeamAccess" class="flex items-center justify-between px-1">
                                            <Label for="team-name" class="py-0 text-base">Team Name </Label>
                                            <p>{{ collateral.team_display_name }}</p>
                                        </div>

                                        <div class="flex items-center justify-between px-1">
                                            <Label for="customer-type" class="py-0 text-base">Customer Type </Label>
                                            <p>{{ collateral.customer_type_selection || collateral.customer_type }}</p>
                                        </div>

                                        <div class="flex items-center justify-between px-1">
                                            <Label for="collateral-type" class="py-0 text-base">Collateral Type </Label>
                                            <p>
                                                {{ collateral.type_selection || collateral.type }}
                                            </p>
                                        </div>

                                        <div v-if="collateral.company_name != null" class="flex items-center justify-between px-1">
                                            <Label for="company-name" class="py-0 text-base">Company Name </Label>
                                            <p>
                                                {{ collateral.company_name }}
                                            </p>
                                        </div>
                                        <div v-if="collateral.business_registration_no != null" class="flex items-center justify-between px-1">
                                            <Label for="business-registration-no" class="py-0 text-base">Business Registration No </Label>
                                            <p>
                                                {{ collateral.business_registration_no }}
                                            </p>
                                        </div>
                                        <div class="flex items-center justify-between px-1">
                                            <Label for="owner-name" class="py-0 text-base">Owner Name </Label>
                                            <p>
                                                {{ collateral.name }}
                                            </p>
                                        </div>

                                        <div class="flex items-center justify-between px-1">
                                            <Label for="owner-ic" class="py-0 text-base">Owner IC </Label>
                                            <p>
                                                {{ collateral.identity_no }}
                                            </p>
                                        </div>
                                        <template v-if="collateral.selection_type_id === 14">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="city" class="py-0 text-base">City/Town/Sub-district </Label>
                                                <p>
                                                    {{ collateral.property.city }}
                                                </p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="location" class="py-0 text-base">Location </Label>
                                                <p>
                                                    {{ collateral.property.location }}
                                                </p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="land-size" class="py-0 text-base">Land Size </Label>
                                                <p>{{ collateral.property.land_size }} {{ collateral.property.land_size_unit_selection }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="district" class="py-0 text-base">District </Label>
                                                <p>
                                                    {{ collateral.property.district }}
                                                </p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="ownership-no" class="py-0 text-base">Ownership No </Label>
                                                <p>
                                                    {{ collateral.property.ownership_no }}
                                                </p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="no-syit-piawai" class="py-0 text-base">No. Syit Piawai </Label>
                                                <p>
                                                    {{ collateral.property.no_syit_piawai }}
                                                </p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="type-of-property" class="py-0 text-base">Type of Property </Label>
                                                <p>
                                                    {{ collateral.property.type_of_property_selection || collateral.property.type_of_property }}
                                                </p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="certified-plan-no" class="py-0 text-base">Certified Plan No </Label>
                                                <p>
                                                    {{ collateral.property.certified_plan_no || '-' }}
                                                </p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="lot-number" class="py-0 text-base">Lot Number </Label>
                                                <p>
                                                    {{ collateral.property.lot_number }}
                                                </p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="land-status" class="py-0 text-base">Land Status </Label>
                                                <p>
                                                    {{ collateral.property.land_status_selection || collateral.property.land_status }}
                                                </p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="land-category" class="py-0 text-base">Land Category </Label>
                                                <p>
                                                    {{ collateral.property.land_category_selection || collateral.land_category }}
                                                </p>
                                            </div>

                                            <div class="flex items-center justify-between px-1">
                                                <Label for="built-up-area" class="py-0 text-base">Built Up Area of Property (Square Feet) </Label>
                                                {{ collateral.property.built_up_area_of_property }}
                                                {{ collateral.property.built_up_area_unit_selection }}
                                            </div>
                                        </template>
                                        <template v-else>
                                            <div class="px-1">
                                                <Label for="remark" class="py-0 text-base">Remark </Label>
                                                <p>
                                                    {{ collateral.remark }}
                                                </p>
                                            </div>
                                        </template>
                                    </div>
                                    <Separator v-if="collateral.selection_type_id === 14" class="my-2" />
                                    <div v-if="collateral.selection_type_id === 14" class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                        <div class="">
                                            <Label for="address" class="py-0 text-base">Address </Label>
                                            <p class="whitespace-pre-wrap">{{ formatAddress(collateral.property.address) }}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </CardContent>
                        <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                            <Button
                                variant="outline"
                                @click="() => $inertia.visit(route('collaterals.index'))"
                                type="button"
                                class="bg-card text-muted-foreground flex items-center gap-2"
                            >
                                Back
                            </Button>
                            <Button
                                variant="outline"
                                @click="() => (activeTab = 'valuation')"
                                type="button"
                                class="bg-green flex items-center gap-2 text-white"
                            >
                                Next
                            </Button>
                        </CardFooter>
                    </template>

                    <template #valuation>
                        <CardContent class="py-4">
                            <Label class="text-[20px]">Valuation</Label>
                            <Accordion type="single" class="w-full" collapsible v-model="openAccordion">
                                <AccordionItem v-for="(valuer, index) in collateral.valuers" :key="index" :value="String(index)" class="mb-1">
                                    <Card class="gap-0 rounded-xs py-0">
                                        <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                            <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                <FaIcon name="plus" />
                                            </span>
                                            <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                <FaIcon name="minus" />
                                            </span>
                                            <span class="flex-1 py-2 text-left font-medium"> {{ ordinal(index + 1) }} Valuation </span>
                                        </AccordionTrigger>

                                        <Separator />
                                        <AccordionContent class="p-2">
                                            <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
                                                <div class="flex items-center justify-between px-1">
                                                    <Label :for="`valuers.${index}.valuer`" class="text-base">Valuer </Label>
                                                    {{ valuer.valuer }}
                                                </div>

                                                <div class="flex items-center justify-between px-1">
                                                    <Label :for="`valuers.${index}.valuation_amount`" class="text-base">Valuation (RM) </Label>
                                                    {{ valuer.valuation_amount }}
                                                </div>

                                                <div class="flex items-center justify-between px-1">
                                                    <Label :for="`valuers.${index}.valuation_received_date`" class="text-base"
                                                        >Valuation Received Date</Label
                                                    >
                                                    {{ valuer.valuation_received_date ? formatDate(valuer.valuation_received_date) : '-' }}
                                                </div>

                                                <div class="flex items-center justify-between px-1">
                                                    <Label :for="`valuers.${index}.land_search_received_date`" class="text-base"
                                                        >Land Search Received Date</Label
                                                    >
                                                    {{ valuer.land_search_received_date ? formatDate(valuer.land_search_received_date) : '-' }}
                                                </div>
                                            </div>
                                        </AccordionContent>
                                    </Card>
                                </AccordionItem>
                            </Accordion>
                        </CardContent>
                        <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                            <Button
                                variant="outline"
                                @click="() => (activeTab = 'info')"
                                type="button"
                                class="bg-card text-muted-foreground flex items-center gap-2"
                            >
                                Back
                            </Button>
                            <Button
                                variant="outline"
                                @click="() => (activeTab = 'owner')"
                                type="button"
                                class="bg-green flex items-center gap-2 text-white"
                            >
                                Next
                            </Button>
                        </CardFooter>
                    </template>

                    <template #owner>
                        <CardContent class="py-4">
                            <Label class="text-[20px]">Additional Owner (Optional)</Label>
                            <Accordion type="single" class="w-full" collapsible v-model="openAccordionIndex">
                                <AccordionItem v-for="(owner, index) in collateral.property_owners" :key="index" :value="String(index)" class="mb-1">
                                    <Card class="gap-0 rounded-xs py-0">
                                        <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                            <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                <FaIcon name="plus" />
                                            </span>
                                            <!-- Minus icon: visible when open -->
                                            <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                <FaIcon name="minus" />
                                            </span>
                                            <span class="flex-1 text-left font-medium"> {{ ordinal(index + 2) }} Owner </span>
                                        </AccordionTrigger>
                                        <Separator />
                                        <AccordionContent class="p-2">
                                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                                <div class="space-y-3">
                                                    <div class="flex items-center justify-between px-1">
                                                        <Label :for="`property_owners.${index}.name`" class="text-base">Name</Label>
                                                        {{ owner.name }}
                                                    </div>

                                                    <div class="flex items-center justify-between px-1">
                                                        <Label :for="`property_owners.${index}.identity_no`" class="text-base">I/C No</Label>
                                                        {{ owner.identity_no || '-' }}
                                                    </div>

                                                    <div class="flex items-center justify-between px-1">
                                                        <Label :for="`property_owners.${index}.telephone`" class="text-base">Telephone</Label>
                                                        {{ owner.telephone ? `${owner.telephone_country_selection} ${owner.telephone}` : '-' }}
                                                    </div>

                                                    <div class="flex items-center justify-between px-1">
                                                        <Label :for="`property_owners.${index}.mobile_phone`" class="text-base">Mobile Phone</Label>
                                                        {{ owner.mobile_phone ? `${owner.mobile_country_selection} ${owner.mobile_phone}` : '-' }}
                                                    </div>
                                                </div>
                                                <div class="space-y-3">
                                                    <div>
                                                        <Label :for="`property_owners.${index}.address.line_1`" class="pb-2 text-base">Address</Label>
                                                        <p class="whitespace-pre-wrap">{{ formatAddress(owner.address) || '-' }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </AccordionContent>
                                    </Card>
                                </AccordionItem>
                            </Accordion>
                        </CardContent>
                        <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                            <Button
                                variant="outline"
                                @click="() => (activeTab = 'valuation')"
                                type="button"
                                class="bg-card text-muted-foreground flex items-center gap-2"
                            >
                                Back
                            </Button>
                        </CardFooter>
                    </template>
                </TabsWrapper>
            </Card>
        </div>
    </AppLayout>
</template>
