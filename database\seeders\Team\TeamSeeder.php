<?php

namespace Database\Seeders\Team;

use App\Models\Company;
use App\Models\Team;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    public function run(): void
    {
        $teams = [
            [
                'company_name' => 'HEADQUARTER001',
                'team' => [
                    'name' => 'HQ Team 1',
                    'code' => 'T1',
                    'contact_no' => '123-456-7890',
                ],
            ],
            [
                'company_name' => 'COMPANY001',
                'team' => [
                    'name' => 'Company Team 2',
                    'code' => 'T2',
                    'contact_no' => '123-456-7890',
                ],
            ],
            [
                'company_name' => 'HEADQUARTER002',
                'team' => [
                    'name' => 'HQ Team 3',
                    'code' => 'T3',
                    'contact_no' => '123-456-7890',
                ],
            ],
            [
                'company_name' => 'COMPANY002',
                'team' => [
                    'name' => 'Company Team 4',
                    'code' => 'T4',
                    'contact_no' => '123-456-7890',
                ],
            ],
        ];

        foreach ($teams as $entry) {
            $company = Company::firstWhere('name', $entry['company_name']);
            if (! $company) {
                $this->command->error("Company '{$entry['company_name']}' not found. Please run CompanySeeder first.");

                continue;
            }

            $teamData = $entry['team'];
            $teamExists = Team::where('name', $teamData['name'])->exists();
            if ($teamExists) {
                continue;
            }

            $team = Team::create([
                'code' => $teamData['code'],
                'headquarter_id' => $company->headquarter_id,
                'company_id' => $company->id,
                'name' => $teamData['name'],
                'contact_no' => $teamData['contact_no'],
            ]);

            $team->companies()->attach($company->id);

            $team->address()->create([
                'category' => 0,
                'line_1' => '123 Main Street',
                'line_2' => 'Suite 456',
                'postcode' => '12345',
                'city' => 'Metropolis',
                'selection_state_id' => null,
                'is_primary' => true,
            ]);
        }

        $this->command->info('Teams created successfully');
    }
}
