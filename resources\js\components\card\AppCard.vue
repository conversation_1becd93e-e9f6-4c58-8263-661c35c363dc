<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useForm } from '@inertiajs/vue3';

interface Props {
    title: string;
    description?: string;
    form?: ReturnType<typeof useForm>;
    backRoute?: string;
    isForm?: boolean;
    submitLabel?: string;
    backLabel?: string;
    processing?: boolean;
    itemId?: number;
    actionRoute?: string;
    actionLabel?: string;
    icon?: string;
    showHeader?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    title: '',
    description: '',
    isForm: false,
    submitLabel: 'Submit',
    backLabel: 'Back',
    processing: false,
    showHeader: true, // Default to showing header
    itemId: undefined,
});

defineEmits<{
    submit: [];
}>();
</script>

<template>
    <component :is="isForm ? 'form' : 'div'" @submit.prevent="isForm ? $emit('submit') : undefined" class="relative">
        <Card :class="['gap-2 border-1', showHeader ? 'py-0' : 'py-4']">
            <CardHeader v-if="showHeader && title" class="bg-azure border-stone gap-0 rounded-t-lg border-b-10 px-5.5 py-3 text-white">
                <CardTitle>{{ title }}</CardTitle>
            </CardHeader>
            <CardContent :class="{ 'px-0': showHeader }">
                <div class="flex flex-col space-y-8 rounded-xl bg-white lg:flex-row lg:space-y-0 lg:space-x-12">
                    <div :class="[isForm ? '' : 'overflow-x-auto', 'flex-1', { 'px-4 py-4 sm:px-6': showHeader }]">
                        <slot></slot>
                    </div>
                </div>
            </CardContent>

            <CardFooter
                v-if="(isForm && form && backRoute) || $slots.footer"
                class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3"
            >
                <Button
                    variant="outline"
                    @click="() => $inertia.visit(route(backRoute))"
                    type="button"
                    class="bg-card text-muted-foreground flex items-center gap-2"
                >
                    <FaIcon name="chevron-left" />
                    Back
                </Button>
                <template v-if="isForm && form && backRoute">
                    <Button class="bg-greenish text-white" :disabled="processing || form.processing" type="submit">
                        {{ submitLabel }}
                        <FaIcon name="paper-plane" />
                    </Button>
                </template>
                <template v-else-if="form && actionRoute">
                    <Button @click="() => form.get(route(actionRoute, props.itemId))" class="bg-green-600 hover:bg-green-700">
                        {{ actionLabel }}
                        <FaIcon :name="icon" />
                    </Button>
                </template>
            </CardFooter>
        </Card>
    </component>
</template>
