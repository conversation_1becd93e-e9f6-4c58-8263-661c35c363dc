<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Selection } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const { formatEnumOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();

interface Props {
    selection: Selection;
    categories: string[];
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    value: props.selection.value,
    description: props.selection.description || '',
    sort_order: props.selection.sort_order,
    status: props.selection.status,
});

const formFields = computed(() => [
    {
        id: 'category',
        label: 'Category',
        type: 'show',
        modelValue: props.selection.category,
    },
    {
        id: 'value',
        label: 'Value',
        type: 'input',
        required: true,
        placeholder: 'Value',
        error: form.errors.value,
        modelValue: form.value,
        updateValue: (value: string) => (form.value = value),
    },
    {
        id: 'description',
        label: 'Description',
        type: 'input',
        required: false,
        placeholder: 'Description',
        error: form.errors.description,
        modelValue: form.description,
        updateValue: (value: string) => (form.description = value),
    },
    {
        id: 'sort_order',
        label: 'Sort Order',
        type: 'input',
        required: false,
        placeholder: 'Sort Order',
        error: form.errors.sort_order,
        modelValue: form.sort_order,
        updateValue: (value: number) => (form.sort_order = value),
    },
    {
        id: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        placeholder: 'Status',
        error: form.errors.status,
        options: formatEnumOptions(props.statuses),
        modelValue: form.status,
        updateValue: (value: number) => (form.status = value),
    },
]);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('selections.update', props.selection.id),
            entityName: 'selection',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head :title="`Edit Selection - ${selection.value}`" />

        <div class="px-4 py-3">
            <Heading title="Selection" pageNumber="P000050" description="Edit the selected selection record" />

            <AppCard title="Edit Selection" :form="form" backRoute="selections.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
