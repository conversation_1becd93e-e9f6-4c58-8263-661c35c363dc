<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * LoanCustomerCollateral model for managing loan customer collateral relationships
 */
class LoanCustomerCollateral extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'loan_id',
        'customer_collateral_id',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_id' => 'integer',
            'customer_collateral_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan that owns this collateral relationship.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class, 'loan_id');
    }

    /**
     * Get the loan customer profile that owns this collateral relationship.
     */
    public function customerCollateral(): BelongsTo
    {
        return $this->belongsTo(CustomerCollateral::class, 'customer_collateral_id');
    }

    /**
     * Get the collateral for this loan customer collateral.
     */
    public function collateral()
    {
        return $this->hasOne(Collateral::class);
    }
}
