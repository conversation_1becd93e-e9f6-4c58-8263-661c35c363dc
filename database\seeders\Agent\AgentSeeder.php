<?php

namespace Database\Seeders\Agent;

use App\Models\AgentProfile;
use App\Models\Company;
use Illuminate\Database\Seeder;

class AgentSeeder extends Seeder
{
    public function run(): void
    {
        $agentsByCompany = [
            'HEADQUARTER001' => [
                [
                    'name' => 'HQ AGENT 001',
                    'display_name' => 'HQ Agent 001',
                    'email' => '<EMAIL>',
                ],
            ],
            'COMPANY001' => [
                [
                    'name' => 'COMPANY AGENT 001',
                    'display_name' => 'Company Agent 001',
                    'email' => '<EMAIL>',
                ],
                [
                    'name' => 'COMPANY AGENT 002',
                    'display_name' => 'Company Agent 002',
                    'email' => '<EMAIL>',
                ],
                [
                    'name' => 'COMPANY AGENT 003',
                    'display_name' => 'Company Agent 003',
                    'email' => '<EMAIL>',
                ],
            ],
        ];

        foreach ($agentsByCompany as $companyName => $agents) {
            $company = Company::firstWhere('name', $companyName);

            if (! $company) {
                $this->command->error("Company '{$companyName}' not found. Please run CompanySeeder first.");

                continue;
            }

            foreach ($agents as $agentData) {
                if (! AgentProfile::where('name', $agentData['name'])->exists()) {
                    $agent = AgentProfile::firstOrCreate(
                        [
                            'name' => $agentData['name'],
                        ],
                        [
                            'company_id' => $company->id,
                            'display_name' => $agentData['display_name'],
                            'email' => $agentData['email'],
                        ]
                    );

                    $agent->companies()->attach($company->id);
                }
            }
        }

        $this->command->info('Agent created successfully');
    }
}
