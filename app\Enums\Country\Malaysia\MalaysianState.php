<?php

namespace App\Enums\Country\Malaysia;

enum MalaysianState: string
{
    case JOHOR = 'johor';
    case KEDAH = 'kedah';
    case KELANTAN = 'kelantan';
    case MELAKA = 'melaka';
    case NEGERI_SEMBILAN = 'negeri_sembilan';
    case PAHANG = 'pahang';
    case PERAK = 'perak';
    case PERLIS = 'perlis';
    case PULAU_PINANG = 'pulau_pinang';
    case SELANGOR = 'selangor';
    case TERENGGANU = 'terengganu';
    case SABAH = 'sabah';
    case SARAWAK = 'sarawak';
    case KUALA_LUMPUR = 'kuala_lumpur';
    case LABUAN = 'labuan';
    case PUTRAJAYA = 'putrajaya';

    /**
     * Get all available states as an array
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return [
            self::JOHOR->value => 'Johor',
            self::KEDAH->value => 'Kedah',
            self::KELANTAN->value => 'Kelantan',
            self::MELAKA->value => 'Melaka',
            self::NEGERI_SEMBILAN->value => 'Negeri Sembilan',
            self::PAHANG->value => 'Pahang',
            self::PERAK->value => 'Perak',
            self::PERLIS->value => 'Perlis',
            self::PULAU_PINANG->value => 'Pulau Pinang',
            self::SELANGOR->value => 'Selangor',
            self::TERENGGANU->value => 'Terengganu',
            self::SABAH->value => 'Sabah',
            self::SARAWAK->value => 'Sarawak',
            self::KUALA_LUMPUR->value => 'Kuala Lumpur',
            self::LABUAN->value => 'Labuan',
            self::PUTRAJAYA->value => 'Putrajaya',
        ];
    }

    /**
     * Get the display name for a state
     */
    public function label(): string
    {
        return match ($this) {
            self::JOHOR => 'Johor',
            self::KEDAH => 'Kedah',
            self::KELANTAN => 'Kelantan',
            self::MELAKA => 'Melaka',
            self::NEGERI_SEMBILAN => 'Negeri Sembilan',
            self::PAHANG => 'Pahang',
            self::PERAK => 'Perak',
            self::PERLIS => 'Perlis',
            self::PULAU_PINANG => 'Pulau Pinang',
            self::SELANGOR => 'Selangor',
            self::TERENGGANU => 'Terengganu',
            self::SABAH => 'Sabah',
            self::SARAWAK => 'Sarawak',
            self::KUALA_LUMPUR => 'Kuala Lumpur',
            self::LABUAN => 'Labuan',
            self::PUTRAJAYA => 'Putrajaya',
        };
    }
}
