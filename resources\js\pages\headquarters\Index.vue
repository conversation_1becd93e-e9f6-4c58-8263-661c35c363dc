<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import type { Headquarter } from '@/types';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { formatEnumOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();

interface Props {
    headquarters: PaginatedData<Headquarter>;
    filters: FilterOptions & {
        name?: string;
        status?: number;
    };
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({});

const searchValue = ref(props.filters.name || '');
const headquarterStatus = ref(props.filters.status || '');

const debouncedSearch = useDebounceFn((value: string) => {
    form.get(
        route('headquarters.index', {
            name: value,
            status: headquarterStatus.value,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = (value: string) => {
    debouncedSearch(value);
};

const handleReset = () => {
    searchValue.value = '';
    headquarterStatus.value = '';
    form.get(route('headquarters.index'));
};

const handleView = (headquarter: Headquarter) => {
    form.get(route('headquarters.show', headquarter.id));
};

const handleEdit = (headquarter: Headquarter) => {
    form.get(route('headquarters.edit', headquarter.id));
};

const handleToggleStatus = (data: { row: Headquarter; newStatus: number }) => {
    const { row: headquarter, newStatus } = data;
    updateStatus(headquarter, newStatus);
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';

    form.get(
        route('headquarters.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const updateStatus = (headquarter: Headquarter, newStatus: number) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Do you want to change the status of ${headquarter.display_name} to ${newStatus === 0 ? 'Active' : 'Inactive'}?`,
        },
        submitOptions: {
            method: 'put',
            url: route('headquarters.update-status', headquarter.id),
            transform: (data) => ({
                ...data,
                status: newStatus,
            }),
            successMessage: `Status of ${headquarter.display_name} has been updated.`,
            errorMessage: `Unable to update status for ${headquarter.display_name}. Please try again.`,
            entityName: 'headquarter',
        },
    });
};

const columns = [
    { field: 'code', label: 'Headquarter Code', sortable: true, width: 'w-40' },
    { field: 'display_name', label: 'Headquarter Name', sortable: true },
    { field: 'updated_at', label: 'Updated At', sortable: true, width: 'w-40', format: (value) => formatDateTime(value) },
    { field: 'updated_by.name', label: 'Updated By', sortable: true },
    { field: 'status', label: 'Status', sortable: true, width: 'w-30' },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Headquarter" />

        <div class="px-4 py-3">
            <Heading title="Headquarter" pageNumber="P000001" />

            <SearchCard
                v-model:searchValue="searchValue"
                v-model:searchStatus="headquarterStatus"
                searchLabel="Headquarter Name"
                searchPlaceholder="Headquarter Name"
                :status="true"
                :statusOptions="formatEnumOptions(props.statuses)"
                @search="handleSearch"
                @reset="handleReset"
            />

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('headquarters.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Headquarter
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="headquarters.data"
                        :sort-state="sortState"
                        empty-message="No headquarters found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        @toggleStatus="handleToggleStatus"
                        :showDeleteButton="false"
                    >
                        <!-- Custom cell renderers -->
                        <template #cell-status="{ row }">
                            <Badge
                                :class="[
                                    {
                                        'bg-green': row.status === 0,
                                        'bg-canary': row.status === 1,
                                    },
                                    'text-md w-15 px-1 py-0',
                                ]"
                            >
                                {{ row.status === 0 ? 'Active' : 'Inactive' }}
                            </Badge>
                        </template>
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries
                                    :from="headquarters.from"
                                    :to="headquarters.to"
                                    :total="headquarters.total"
                                    entityName="headquarters"
                                />
                            </div>
                            <Pagination :links="headquarters.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
