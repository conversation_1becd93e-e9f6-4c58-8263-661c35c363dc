<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { computed } from 'vue';

interface Role {
    id: number;
    name: string;
}

const props = defineProps<{
    roles: Role[];
    modelValue: number | null;
    error?: string;
    label?: string;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: number | null];
}>();

// Create a local value that syncs with the modelValue prop
const selectedRole = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
});

// Get role name by id
const getRoleName = (roleId: number | null): string => {
    if (!roleId) return '';
    const role = props.roles.find((r) => r.id === roleId);
    return role ? role.name : '';
};
</script>

<template>
    <div>
        <Label v-if="label">{{ label }}</Label>
        <div class="mt-1">
            <Select :model-value="selectedRole" @update:model-value="selectedRole = $event">
                <SelectTrigger class="w-full">
                    <SelectValue :placeholder="selectedRole ? getRoleName(selectedRole) : 'Select role'" />
                </SelectTrigger>
                <SelectContent>
                    <SelectGroup>
                        <SelectLabel>Available Roles</SelectLabel>
                        <SelectItem v-for="role in roles" :key="role.id" :value="role.id">
                            {{ role.name }}
                        </SelectItem>
                    </SelectGroup>
                </SelectContent>
            </Select>
        </div>

        <InputError :message="error" class="mt-2" />
    </div>
</template>
