<?php

namespace App\Http\Controllers\Agents;

use App\Http\Controllers\Controller;
use App\Http\Requests\Agents\StoreOutcomeTypeRequest;
use App\Http\Requests\Agents\UpdateOutcomeTypeRequest;
use App\Http\Resources\Agents\AgentOutcomeTypeResource;
use App\Models\AgentOutcomeType;
use App\Models\Company;
use App\Models\Headquarter;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class OutcomeTypeController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Display a listing of the agent outcome types.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', AgentOutcomeType::class);

        $query = AgentOutcomeType::forUser()
            ->withAuditUsers()
            ->with(['companies']);

        $this->applySearchFilter($query, $request);
        $this->applySorting($query, $request);

        $agentOutcomeTypes = $this->applyPagination($query, $request, 10,
            fn ($agentOutcomeType) => (new AgentOutcomeTypeResource($agentOutcomeType))->toArray($request));

        return Inertia::render('agent-outcome-types/Index', [
            'outcomeTypes' => $agentOutcomeTypes,
            'filters' => $request->only(['name', 'per_page', 'sort_field', 'sort_direction']),
        ]);
    }

    /**
     * Show the form for creating a new agent outcome type.
     */
    public function create(): Response
    {
        $this->authorize('create', AgentOutcomeType::class);

        return Inertia::render('agent-outcome-types/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
        ]);
    }

    /**
     * Store a newly created agent outcome type in storage.
     */
    public function store(StoreOutcomeTypeRequest $request): RedirectResponse
    {
        $this->authorize('create', AgentOutcomeType::class);

        $validated = $request->validated();

        try {
            DB::beginTransaction();

            $agentOutcomeType = AgentOutcomeType::create([
                'company_id' => $validated['company_id'],
                'name' => $validated['name'],
            ]);

            $agentOutcomeType->companies()->attach($validated['company_id']);

            DB::commit();

            return Redirect::route('agent-outcome-types.index')->with('success', 'Agent outcome type created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return Redirect::back()->withInput()->with('error', 'Failed to create agent outcome type: '.$e->getMessage());
        }
    }

    /**
     * Display the specified agent outcome type.
     */
    public function show(AgentOutcomeType $agentOutcomeType): Response
    {
        $this->authorize('view', $agentOutcomeType);

        $agentOutcomeType->withAuditUsers();

        return Inertia::render('agent-outcome-types/Show', [
            'outcomeType' => (new AgentOutcomeTypeResource($agentOutcomeType))->toArray(request()),
        ]);
    }

    /**
     * Show the form for editing the specified agent outcome type.
     */
    public function edit(AgentOutcomeType $agentOutcomeType): Response
    {
        $this->authorize('update', $agentOutcomeType);

        $agentOutcomeType->load(['companies']);

        $company = $agentOutcomeType->companies->first();

        return Inertia::render('agent-outcome-types/Edit', [
            'outcomeType' => [
                'id' => $agentOutcomeType->id,
                'name' => $agentOutcomeType->name,
                'company' => $company?->display_name,
                'headquarter' => $company?->parent?->display_name,
            ],
        ]);
    }

    /**
     * Update the specified agent outcome type in storage.
     */
    public function update(UpdateOutcomeTypeRequest $request, AgentOutcomeType $agentOutcomeType): RedirectResponse
    {
        $this->authorize('update', $agentOutcomeType);

        try {
            $agentOutcomeType->update([
                'name' => $request->name,
            ]);

            return Redirect::route('agent-outcome-types.index')->with('success', 'Agent outcome type updated successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->withInput()->with('error', 'Failed to update agent outcome type: '.$e->getMessage());
        }
    }

    /**
     * Remove the specified agent outcome type from storage.
     */
    public function destroy(AgentOutcomeType $agentOutcomeType): RedirectResponse
    {
        $this->authorize('delete', $agentOutcomeType);

        try {
            $agentOutcomeType->delete();

            return Redirect::route('agent-outcome-types.index')->with('success', 'Agent outcome type deleted successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to delete agent outcome type: '.$e->getMessage());
        }
    }
}
