<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import FormSelect from '@/components/form/FormSelect.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ref, watch } from 'vue';

interface Address {
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    selection_state_id: number | null;
    state: string | null;
    selection_country_id: number | null;
    country: string | null;
}

interface Owner {
    id?: number;
    name: string;
    identity_no: string;
    selection_telephone_country_id?: number | null;
    selection_mobile_country_id?: number | null;
    telephone: string | null;
    mobile_phone: string | null;
    remark: string | null;
    address: Address;
    _delete?: boolean;
}

interface Selection {
    id: number | null;
    value: string | null;
}

const props = defineProps<{
    property_owners: Owner[];
    errors?: Record<string, string>;
    states: Selection[];
    countries: Selection[];
    mobileCountries: Selection[];
    telephoneCountries: Selection[];
    ordinal: (n: number) => string;
    isEdit: boolean;
    collateralSide?: boolean;
    goBack?: () => void;
    submit?: () => void;
}>();
const openAccordionIndex = ref<string | undefined>(undefined);

const addPropertyOwner = () => {
    props.property_owners.push({
        name: '',
        identity_no: '',
        selection_telephone_country_id: props.telephoneCountries[0]?.id ?? null,
        selection_mobile_country_id: props.mobileCountries[0]?.id ?? null,
        telephone: null,
        mobile_phone: null,
        remark: null,
        address: {
            line_1: null,
            line_2: null,
            postcode: null,
            city: null,
            selection_state_id: null,
            state: null,
            selection_country_id: null,
            country: null,
        },
    });
    openAccordionIndex.value = (props.property_owners.length - 1).toString();
};

const removePropertyOwner = (index: number) => {
    if (props.property_owners[index].id) {
        props.property_owners[index]._delete = true;
    } else {
        props.property_owners.splice(index, 1);
    }
};

watch(
    () => props.property_owners.map((owner) => owner.address.selection_country_id),
    (newCountryIds, oldCountryIds) => {
        if (!newCountryIds || !oldCountryIds) return;

        newCountryIds.forEach((newCountryId, index) => {
            if (newCountryId !== oldCountryIds[index]) {
                props.property_owners[index].address.selection_state_id = null;
            }
        });
    },
    { deep: true },
);
</script>

<template>
    <CardContent :class="['py-4', !collateralSide && 'px-0']">
        <div class="flex items-center justify-between pb-3">
            <Label class="text-[20px]">Additional Owner (Optional)</Label>
            <Button type="button" class="bg-teal hover:bg-teal-hover flex items-center gap-2" @click="addPropertyOwner">
                <FaIcon name="plus" />
                Add New
            </Button>
        </div>
        <Accordion type="single" class="w-full" collapsible v-model="openAccordionIndex">
            <AccordionItem v-for="(owner, index) in property_owners" :key="index" :value="String(index)" class="mb-1">
                <Card v-if="!owner._delete" class="gap-0 rounded-xs py-0">
                    <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                        <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                            <FaIcon name="plus" />
                        </span>

                        <!-- Minus icon: visible when open -->
                        <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                            <FaIcon name="minus" />
                        </span>

                        <span class="flex-1 text-left font-medium"> {{ ordinal(index + 2) }} Owner </span>

                        <template #icon>
                            <Button type="button" @click.stop="removePropertyOwner(index)" variant="destructive" class="flex items-center gap-1">
                                <FaIcon name="trash" />
                                Delete
                            </Button>
                        </template>
                    </AccordionTrigger>
                    <Separator />
                    <AccordionContent class="p-2">
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <!-- Owner Name -->
                            <div>
                                <Label :for="`property_owners.${index}.name`" class="text-base"
                                    >Name
                                    <RequiredIndicator />
                                </Label>
                                <p v-if="isEdit && owner?.id">{{ owner.name }}</p>
                                <Input
                                    v-else
                                    :id="`property_owners.${index}.name`"
                                    v-model="owner.name"
                                    :error="errors[`property_owners.${index}.name`]"
                                    required
                                    placeholder="Name"
                                />
                                <div v-if="errors[`property_owners.${index}.name`]" class="mt-1 text-sm text-red-500">
                                    {{ errors[`property_owners.${index}.name`] }}
                                </div>
                            </div>

                            <!-- IC Number -->
                            <div>
                                <Label :for="`property_owners.${index}.identity_no`" class="text-base"
                                    >I/C No
                                    <RequiredIndicator />
                                </Label>
                                <p v-if="isEdit && owner?.id">{{ owner.identity_no }}</p>
                                <Input
                                    v-else
                                    :id="`property_owners.${index}.identity_no`"
                                    v-model="owner.identity_no"
                                    :error="errors[`property_owners.${index}.identity_no`]"
                                    required
                                    placeholder="IC No"
                                />
                                <div v-if="errors[`property_owners.${index}.identity_no`]" class="mt-1 text-sm text-red-500">
                                    {{ errors[`property_owners.${index}.identity_no`] }}
                                </div>
                            </div>

                            <!-- Telephone -->
                            <div>
                                <Label :for="`property_owners.${index}.selection_telephone_country_id`" class="text-base">Telephone</Label>
                                <div class="flex">
                                    <Select
                                        v-model="owner.selection_telephone_country_id"
                                        :error="errors[`property_owners.${index}.selection_telephone_country_id`]"
                                    >
                                        <SelectTrigger class="bg-cloud w-25 !rounded-r-none">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="type in telephoneCountries" :key="type.id" :value="type.id">
                                                {{ type.value }}
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Input
                                        :id="`property_owners.${index}.telephone`"
                                        v-model="owner.telephone"
                                        :error="errors[`property_owners.${index}.telephone`]"
                                        placeholder="Telephone"
                                        class="w-full !rounded-l-none"
                                    />
                                </div>
                                <div v-if="errors[`property_owners.${index}.telephone`]" class="mt-1 text-sm text-red-500">
                                    {{ errors[`property_owners.${index}.telephone`] }}
                                </div>
                            </div>

                            <!-- Mobile Phone -->
                            <div>
                                <Label :for="`property_owners.${index}.selection_mobile_country_id`" class="text-base">Mobile Phone</Label>
                                <div class="flex">
                                    <Select
                                        v-model="owner.selection_mobile_country_id"
                                        :error="errors[`property_owners.${index}.selection_mobile_country_id`]"
                                    >
                                        <SelectTrigger class="bg-cloud w-25 !rounded-r-none">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="type in mobileCountries" :key="type.id" :value="type.id">
                                                {{ type.value }}
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Input
                                        :id="`property_owners.${index}.mobile_phone`"
                                        v-model="owner.mobile_phone"
                                        :error="errors[`property_owners.${index}.mobile_phone`]"
                                        placeholder="Mobile Phone"
                                        class="w-full !rounded-l-none"
                                    />
                                </div>
                                <div v-if="errors[`property_owners.${index}.mobile_phone`]" class="mt-1 text-sm text-red-500">
                                    {{ errors[`property_owners.${index}.mobile_phone`] }}
                                </div>
                            </div>
                        </div>

                        <!-- Owner Address -->
                        <div class="mt-6">
                            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <!-- Address Line 1 -->
                                <div class="col-span-2">
                                    <Label :for="`property_owners.${index}.address.line_1`" class="text-base">Address Line 1</Label>
                                    <Input
                                        :id="`property_owners.${index}.address.line_1`"
                                        v-model="owner.address.line_1"
                                        :error="errors[`property_owners.${index}.address.line_1`]"
                                        placeholder="Address Line 1"
                                    />
                                    <div v-if="errors[`property_owners.${index}.address.line_1`]" class="mt-1 text-sm text-red-500">
                                        {{ errors[`property_owners.${index}.address.line_1`] }}
                                    </div>
                                </div>

                                <!-- Address Line 2 -->
                                <div class="col-span-2">
                                    <Label :for="`property_owners.${index}.address.line_2`" class="text-base">Address Line 2</Label>
                                    <Input
                                        :id="`property_owners.${index}.address.line_2`"
                                        v-model="owner.address.line_2"
                                        :error="errors[`property_owners.${index}.address.line_2`]"
                                        placeholder="Address Line 2"
                                    />
                                    <div v-if="errors[`property_owners.${index}.address.line_2`]" class="mt-1 text-sm text-red-500">
                                        {{ errors[`property_owners.${index}.address.line_2`] }}
                                    </div>
                                </div>

                                <!-- Postcode -->
                                <div>
                                    <Label :for="`property_owners.${index}.address.postcode`" class="text-base">Postcode</Label>
                                    <Input
                                        :id="`property_owners.${index}.address.postcode`"
                                        v-model="owner.address.postcode"
                                        :error="errors[`property_owners.${index}.address.postcode`]"
                                        placeholder="Postcode"
                                    />
                                    <div v-if="errors[`property_owners.${index}.address.postcode`]" class="mt-1 text-sm text-red-500">
                                        {{ errors[`property_owners.${index}.address.postcode`] }}
                                    </div>
                                </div>

                                <!-- City -->
                                <div>
                                    <Label :for="`property_owners.${index}.address.city`" class="text-base">City</Label>
                                    <Input
                                        :id="`property_owners.${index}.address.city`"
                                        v-model="owner.address.city"
                                        :error="errors[`property_owners.${index}.address.city`]"
                                        placeholder="City"
                                    />
                                    <div v-if="errors[`property_owners.${index}.address.city`]" class="mt-1 text-sm text-red-500">
                                        {{ errors[`property_owners.${index}.address.city`] }}
                                    </div>
                                </div>

                                <!-- State Selection -->
                                <div>
                                    <Label :for="`property_owners.${index}.address.selection_state_id`" class="text-base">State</Label>
                                    <FormSelect
                                        :id="`property_owners.${index}.address.selection_state_id`"
                                        label=""
                                        :model-value="owner.address.selection_state_id"
                                        @update:model-value="owner.address.selection_state_id = $event"
                                        :options="states"
                                        placeholder="Select state"
                                        :error="errors?.[`property_owners.${index}.address.selection_state_id`]"
                                        :filter-by-country="true"
                                        :selected-country-id="owner.address.selection_country_id"
                                        labelClass="hidden"
                                    />
                                    <div v-if="errors?.[`property_owners.${index}.address.selection_state_id`]" class="mt-1 text-sm text-red-500">
                                        {{ errors[`property_owners.${index}.address.selection_state_id`] }}
                                    </div>
                                </div>

                                <!-- Country Selection -->
                                <div>
                                    <Label :for="`property_owners.${index}.address.selection_country_id`" class="text-base">Country</Label>
                                    <Select
                                        v-model="owner.address.selection_country_id"
                                        :error="errors[`property_owners.${index}.address.selection_country_id`]"
                                    >
                                        <SelectTrigger class="w-full">
                                            <SelectValue placeholder="Select country" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="country in countries" :key="country.id" :value="country.id">
                                                {{ country.value }}
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <div v-if="errors[`property_owners.${index}.address.selection_country_id`]" class="mt-1 text-sm text-red-500">
                                        {{ errors[`property_owners.${index}.address.selection_country_id`] }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </Card>
            </AccordionItem>
        </Accordion>
    </CardContent>
    <CardFooter v-if="collateralSide" class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
        <Button variant="outline" @click="goBack" type="button" class="bg-card text-muted-foreground flex items-center gap-2">
            <FaIcon name="chevron-left" />
            Back
        </Button>
        <Button type="button" @click="submit" class="bg-green flex items-center gap-2 text-white">
            Submit
            <FaIcon name="paper-plane" />
        </Button>
    </CardFooter>
</template>
