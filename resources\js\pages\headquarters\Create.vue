<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FileUpload from '@/components/form/file_upload/FileUpload.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const { formatEnumOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();

interface Props {
    defaultStatus: number;
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    display_name: '',
    business_registration_no: '',
    old_business_registration_no: '',
    status: props.defaultStatus,
    logo: '',
});

const formFields = computed(() => [
    {
        id: 'display_name',
        label: 'Headquarter Name',
        type: 'input',
        required: true,
        placeholder: 'Headquarter Name',
        error: form.errors.display_name,
        modelValue: form.display_name,
        updateValue: (value: string) => (form.display_name = value),
    },
    {
        id: 'business_registration_no',
        label: 'New Business Registration No.',
        type: 'input',
        required: false,
        placeholder: 'New Business Registration No.',
        error: form.errors.business_registration_no,
        modelValue: form.business_registration_no,
        updateValue: (value: string) => (form.business_registration_no = value),
    },
    {
        id: 'old_business_registration_no',
        label: 'Old Business Registration No.',
        type: 'input',
        required: false,
        placeholder: 'Old Business Registration No.',
        error: form.errors.old_business_registration_no,
        modelValue: form.old_business_registration_no,
        updateValue: (value: string) => (form.old_business_registration_no = value),
    },
    {
        id: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        placeholder: 'Status',
        error: form.errors.status,
        options: formatEnumOptions(props.statuses),
        modelValue: form.status,
        updateValue: (value: number) => (form.status = value),
    },
]);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('headquarters.store'),
            entityName: 'headquarter',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Create Headquarter" />
        <div class="px-4 py-3">
            <Heading title="Headquarter" pageNumber="P000002" description="Create a new headquarter record" />

            <AppCard title="Add New Headquarter" :form="form" backRoute="headquarters.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                            />
                            <FileUpload id="logo" v-model="form.logo" />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
