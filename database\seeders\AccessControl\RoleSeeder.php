<?php

namespace Database\Seeders\AccessControl;

use App\Enums\AccessControl\RoleName;
use Database\Seeders\VersionedSeeder;
use Spatie\Permission\Models\Role;

class RoleSeeder extends VersionedSeeder
{
    protected string $seederName = 'roles';

    private array $roleData = [
        RoleName::SUPER_ADMIN->value => [
            'level' => 999,
            'can_see_same_level' => true,
            'is_required_headquarter' => false,
            'is_required_company' => false,
            'is_required_team' => false,
            'is_headquarter' => false,
        ],
        RoleName::HEADQUARTER_ADMIN->value => [
            'level' => 800,
            'can_see_same_level' => false,
            'is_required_headquarter' => true,
            'is_required_company' => false,
            'is_required_team' => false,
            'is_headquarter' => true,
        ],
        RoleName::HEADQUARTER_LOAN_OFFICER->value => [
            'level' => 600,
            'can_see_same_level' => false,
            'is_required_headquarter' => true,
            'is_required_company' => true,
            'is_required_team' => true,
            'is_headquarter' => true,
        ],
        RoleName::HEADQUARTER_LOAN_REVIEWER->value => [
            'level' => 600,
            'can_see_same_level' => false,
            'is_required_headquarter' => true,
            'is_required_company' => true,
            'is_required_team' => true,
            'is_headquarter' => true,
        ],
        RoleName::HEADQUARTER_LOAN_APPROVER->value => [
            'level' => 600,
            'can_see_same_level' => false,
            'is_required_headquarter' => true,
            'is_required_company' => true,
            'is_required_team' => true,
            'is_headquarter' => true,
        ],
        RoleName::COMPANY_ADMIN->value => [
            'level' => 500,
            'can_see_same_level' => false,
            'is_required_headquarter' => true,
            'is_required_company' => true,
            'is_required_team' => false,
            'is_headquarter' => false,
        ],
        RoleName::TEAM_ADMIN->value => [
            'level' => 300,
            'can_see_same_level' => false,
            'is_required_headquarter' => true,
            'is_required_company' => true,
            'is_required_team' => true,
            'is_headquarter' => false,
        ],
        RoleName::TEAM_LOAN_OFFICER->value => [
            'level' => 200,
            'can_see_same_level' => false,
            'is_required_headquarter' => true,
            'is_required_company' => true,
            'is_required_team' => true,
            'is_headquarter' => false,
        ],
        RoleName::TEAM_LOAN_REVIEWER->value => [
            'level' => 200,
            'can_see_same_level' => false,
            'is_required_headquarter' => true,
            'is_required_company' => true,
            'is_required_team' => true,
            'is_headquarter' => false,
        ],
        RoleName::TEAM_LOAN_APPROVER->value => [
            'level' => 200,
            'can_see_same_level' => false,
            'is_required_headquarter' => true,
            'is_required_company' => true,
            'is_required_team' => true,
            'is_headquarter' => false,
        ],
    ];

    public function run(): void
    {
        $this->applyVersionedSeeds($this->getVersionedData());
    }

    protected function applyVersion(int $version, array $roles): void
    {
        foreach ($roles as $roleName) {
            $attributes = $this->roleData[$roleName] ?? [
                'level' => 0,
                'can_see_same_level' => false,
                'is_required_headquarter' => true,
                'is_required_company' => true,
                'is_required_team' => true,
                'is_headquarter' => false,
            ];

            Role::firstOrCreate(
                ['name' => $roleName],
                $attributes
            );

            $this->command->line("Created role: {$roleName}");
        }
    }

    protected function getVersionedData(): array
    {
        return [
            1 => array_map(fn (RoleName $role) => $role->value, RoleName::cases()),
        ];
    }
}
