import { clsx, type ClassValue } from 'clsx';
import { format, parseISO } from 'date-fns';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

export function formatDate(date: string | null, formatString: string = 'PPP'): string {
    if (!date) return '';
    try {
        return format(parseISO(date), formatString);
    } catch (error) {
        console.error('Error formatting date:', error);
        return '';
    }
}
