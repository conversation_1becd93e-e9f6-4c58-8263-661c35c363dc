<?php

namespace App\Policies\Companies;

use App\Enums\AccessControl\PermissionName;
use App\Models\Headquarter;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class HeadquarterPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo(PermissionName::READ_HEADQUARTERS->value);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Headquarter $headquarter): bool
    {
        return $user->hasPermissionTo(PermissionName::READ_HEADQUARTERS->value);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo(PermissionName::CREATE_HEADQUARTERS->value);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Headquarter $headquarter): bool
    {
        return $user->hasPermissionTo(PermissionName::UPDATE_HEADQUARTERS->value);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Headquarter $headquarter): bool
    {
        return $user->hasPermissionTo(PermissionName::DELETE_HEADQUARTERS->value);
    }
}
