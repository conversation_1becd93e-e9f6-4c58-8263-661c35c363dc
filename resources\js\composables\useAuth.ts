import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

interface Organization {
    access: {
        headquarter: boolean;
        company: boolean;
        team: boolean;
    };
    primary: {
        headquarter_id: number | null;
        company_id: number | null;
        team_id: number | null;
    };
}

interface User {
    id: number;
    roles: string[];
    permissions: string[];
    organization: Organization;
    is_headquarter: boolean;
    [key: string]: any;
}

export function useAuth() {
    const page = usePage();
    const user = computed<User | undefined>(() => (page.props.auth as { user: User }).user);

    // Super admin check
    const isSuperAdmin = computed<boolean>(() => {
        return hasRole('Super Administrator');
    });

    // Headquarter user check
    const isHeadquarter = computed<boolean>(() => {
        return user.value?.is_headquarter || false;
    });

    // Role helpers
    const hasRole = (role: string): boolean => {
        return user.value?.roles.includes(role) || false;
    };

    const hasAnyRole = (roles: string[]): boolean => {
        return roles.some((role) => hasRole(role));
    };

    const hasAllRoles = (roles: string[]): boolean => {
        return roles.every((role) => hasRole(role));
    };

    // Permission helpers
    const hasPermission = (permission: string): boolean => {
        if (isSuperAdmin.value) return true;

        return user.value?.permissions.includes(permission) || false;
    };

    const hasAnyPermission = (permissions: string[]): boolean => {
        if (isSuperAdmin.value) return true;

        return permissions.some((permission) => hasPermission(permission));
    };

    // Organization access helpers
    const hasHeadquarterAccess = computed<boolean>(() => {
        return user.value?.organization.access.headquarter || false;
    });

    const hasCompanyAccess = computed<boolean>(() => {
        return user.value?.organization.access.company || false;
    });

    const hasTeamAccess = computed<boolean>(() => {
        return user.value?.organization.access.team || false;
    });

    // Primary organization IDs
    const primaryHeadquarterId = computed<number | null>(() => {
        return user.value?.organization.primary.headquarter_id || null;
    });

    const primaryCompanyId = computed<number | null>(() => {
        return user.value?.organization.primary.company_id || null;
    });

    const primaryTeamId = computed<number | null>(() => {
        return user.value?.organization.primary.team_id || null;
    });

    return {
        user,
        hasRole,
        hasPermission,
        hasAnyRole,
        hasAllRoles,
        hasAnyPermission,
        isSuperAdmin,
        isHeadquarter,
        hasHeadquarterAccess,
        hasCompanyAccess,
        hasTeamAccess,
        primaryHeadquarterId,
        primaryCompanyId,
        primaryTeamId,
    };
}
