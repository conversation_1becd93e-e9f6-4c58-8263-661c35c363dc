<?php

namespace App\Http\Resources\Collaterals;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollateralDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $propertyOwners = [];

        if ($this->property) {
            foreach ($this->property->propertyOwners as $owner) {
                $propertyOwners[] = [
                    'id' => $owner->id,
                    'name' => $owner->name,
                    'identity_no' => $owner->identity_no,
                    'selection_telephone_country_id' => $owner->telephone()?->selection_country_id,
                    'selection_mobile_country_id' => $owner->mobilePhone()?->selection_country_id,
                    'telephone_country_selection' => $owner->telephone()?->contactCountrySelection ? $owner->telephone()->contactCountrySelection->value : null,
                    'mobile_country_selection' => $owner->mobilePhone()?->contactCountrySelection ? $owner->mobilePhone()->contactCountrySelection->value : null,
                    'telephone' => $owner->telephone,
                    'mobile_phone' => $owner->mobilePhone,
                    'remark' => $owner->remark,
                    'address' => $owner->address ? [
                        'id' => $owner->address->id,
                        'line_1' => $owner->address->line_1,
                        'line_2' => $owner->address->line_2,
                        'postcode' => $owner->address->postcode,
                        'city' => $owner->address->city,
                        'state' => $owner->address->state,
                        'selection_state_id' => $owner->address->selection_state_id,
                        'state_selection' => $owner->address->stateSelection ? $owner->address->stateSelection->value : null,
                        'country' => $owner->address->country,
                        'selection_country_id' => $owner->address->selection_country_id,
                        'country_selection' => $owner->address->countrySelection ? $owner->address->countrySelection->value : null,
                    ] : null,
                ];
            }
        }

        $valuers = [];

        foreach ($this->valuers as $valuer) {
            $valuers[] = [
                'id' => $valuer->id,
                'valuation_amount' => $valuer->valuation_amount,
                'valuer' => $valuer->valuer,
                'valuation_received_date' => $valuer->valuation_received_date,
                'land_search_received_date' => $valuer->land_search_received_date,
            ];
        }

        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'headquarter_id' => $this->company->headquarter_id,
            'headquarter_display_name' => $this->company->headquarter->display_name,
            'company_id' => $this->company_id,
            'company_display_name' => $this->company->display_name,
            'team_id' => $this->team_id,
            'team_display_name' => $this->team->name,
            'selection_customer_type_id' => $this->selection_customer_type_id,
            'customer_type' => $this->customer_type,
            'customer_type_selection' => $this->customerTypeSelection ? $this->customerTypeSelection->value : null,
            'selection_type_id' => $this->selection_type_id,
            'type_selection' => $this->typeSelection ? $this->typeSelection->value : null,
            'name' => $this->name,
            'identity_no' => $this->identity_no,
            'company_name' => $this->company_name,
            'business_registration_no' => $this->business_registration_no,
            'status' => $this->status->value,
            'status_label' => $this->status->label(),
            'remark' => $this->remark,
            'property' => $this->property ? [
                'id' => $this->property->id,
                'ownership_no' => $this->property->ownership_no,
                'lot_number' => $this->property->lot_number,
                'selection_land_category_id' => $this->property->selection_land_category_id,
                'land_category' => $this->property->land_category,
                'land_category_selection' => $this->property->landCategorySelection ? $this->property->landCategorySelection->value : null,
                'land_category_other' => $this->property->land_category_other,
                'type_of_property' => $this->property->type_of_property,
                'selection_type_of_property_id' => $this->property->selection_type_of_property_id,
                'type_of_property_selection' => $this->property->propertyTypesSelection ? $this->property->propertyTypesSelection->value : null,
                'selection_land_size_unit' => $this->property->selection_land_size_unit,
                'land_size_unit_selection' => $this->property->landSizeSelection ? $this->property->landSizeSelection->value : null,
                'land_size' => $this->property->land_size,
                'selection_land_status_id' => $this->property->selection_land_status_id,
                'land_status' => $this->property->land_status,
                'land_status_selection' => $this->property->landStatusSelection ? $this->property->landStatusSelection->value : null,
                'no_syit_piawai' => $this->property->no_syit_piawai,
                'certified_plan_no' => $this->property->certified_plan_no,
                'selection_built_up_area_unit' => $this->property->selection_built_up_area_unit,
                'built_up_area_of_property' => $this->property->built_up_area_of_property,
                'built_up_area_unit_selection' => $this->property->builtUpAreaSelection ? $this->property->builtUpAreaSelection->value : null,
                'city' => $this->property->city,
                'location' => $this->property->location,
                'district' => $this->property->district,
                'address' => $this->property->address ? [
                    'id' => $this->property->address->id,
                    'line_1' => $this->property->address->line_1,
                    'line_2' => $this->property->address->line_2,
                    'postcode' => $this->property->address->postcode,
                    'city' => $this->property->address->city,
                    'state' => $this->property->address->state,
                    'selection_state_id' => $this->property->address->selection_state_id,
                    'state_selection' => $this->property->address->stateSelection ? $this->property->address->stateSelection->value : null,
                    'country' => $this->property->address->country,
                    'selection_country_id' => $this->property->address->selection_country_id,
                    'country_selection' => $this->property->address->countrySelection ? $this->property->address->countrySelection->value : null,
                ] : null,
            ] : null,
            'property_owners' => $propertyOwners,
            'valuers' => $valuers,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->createdBy ? [
                'id' => $this->createdBy->id,
                'username' => $this->createdBy->username,
            ] : null,
            'updated_by' => $this->updatedBy ? [
                'id' => $this->updatedBy->id,
                'username' => $this->updatedBy->username,
            ] : null,
        ];
    }
}
