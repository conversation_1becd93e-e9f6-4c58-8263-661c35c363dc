<script setup lang="ts">
import { type DateValue, DateFormatter, getLocalTimeZone, parseAbsoluteToLocal, parseDate, today } from '@internationalized/date';

import FaIcon from '@/components/FaIcon.vue';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { computed, ref, watch } from 'vue';

// Props
const props = defineProps<{
    modelValue: string | null;
    disableBeforeToday?: boolean;
    disableAfterToday?: boolean;
    error?: string;
    placeholder?: string;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: string | null): void;
}>();

const tz = getLocalTimeZone();
const todayDate = today(tz);
const df = new DateFormatter('en-US', { dateStyle: 'long' });

const tryParseDate = (input: string) => (input.includes('T') ? parseAbsoluteToLocal(input.replace(/\[.*\]$/, '')) : parseDate(input));

// Format as UTC midnight with .000000Z precision
const formatAsUtcMidnight = (date: Date) =>
    new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate())).toISOString().replace('.000Z', '.000000Z');

const value = ref<DateValue | null>(props.modelValue ? tryParseDate(props.modelValue) : null);

// Watch internal value and emit formatted string
watch(value, (newVal) => {
    emit('update:modelValue', newVal ? formatAsUtcMidnight(newVal.toDate(tz)) : null);
});

// Sync internal value when modelValue prop changes
watch(
    () => props.modelValue,
    (val) => {
        value.value = val ? tryParseDate(val) : null;
    },
);

// Restrict selectable date range
const minDate = computed(() => (props.disableBeforeToday ? todayDate : undefined));
const maxDate = computed(() => (props.disableAfterToday ? todayDate : undefined));
</script>

<template>
    <Popover>
        <PopoverTrigger as-child>
            <Button
                variant="outline"
                :class="
                    cn('w-full justify-between bg-white text-left font-normal', !value && 'text-muted-foreground', props.error && 'border-red-500')
                "
            >
                {{ value ? df.format(value.toDate(getLocalTimeZone())) : placeholder }}
                <FaIcon name="calendar" />
            </Button>
        </PopoverTrigger>
        <PopoverContent class="w-auto p-0">
            <Calendar v-model="value" :min-value="minDate" :max-value="maxDate" initial-focus />
        </PopoverContent>
    </Popover>
</template>
