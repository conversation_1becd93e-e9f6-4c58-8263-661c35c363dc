<?php

namespace App\Models;

use App\Enums\Loan\LoanTxnStatus;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * LoanTxnDetail model for managing loan transaction details
 */
class LoanTxnDetail extends BaseModel
{
    use HasFactory, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'code',
        'loan_txn_id',
        'loan_txn_type_id',
        'txn_type',
        'txn_date',
        'amount',
        'precision_amount',
        'status',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_txn_id' => 'integer',
            'loan_txn_type_id' => 'integer',
            'txn_date' => 'datetime',
            'amount' => 'decimal:2',
            'precision_amount' => 'decimal:7',
            'status' => LoanTxnStatus::class,
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan transaction that owns this detail.
     */
    public function loanTxn(): BelongsTo
    {
        return $this->belongsTo(LoanTxn::class);
    }

    /**
     * Get the loan transaction type that owns this detail.
     */
    public function loanTxnType(): BelongsTo
    {
        return $this->belongsTo(LoanTxnType::class);
    }

    /**
     * Get the user who created this transaction detail.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this transaction detail.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who deleted this transaction detail.
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate code if not provided
        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new loan transaction detail.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix('LTXD');
    }

    /**
     * Scope a query to get transaction details for dropdown lists.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDropdown($query)
    {
        return $query->select('id', 'code', 'amount', 'txn_date', 'status')
            ->orderBy('txn_date', 'desc');
    }

    /**
     * Scope a query to only include unpaid transaction details.
     */
    public function scopeUnpaid($query)
    {
        return $query->where('status', LoanTxnStatus::UNPAID);
    }

    /**
     * Scope a query to only include paid transaction details.
     */
    public function scopePaid($query)
    {
        return $query->where('status', LoanTxnStatus::PAID);
    }

    /**
     * Scope a query to only include partially paid transaction details.
     */
    public function scopePartiallyPaid($query)
    {
        return $query->where('status', LoanTxnStatus::PARTIALLY_PAID);
    }

    /**
     * Scope a query to only include cancelled transaction details.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', LoanTxnStatus::CANCELLED);
    }

    /**
     * Scope a query to filter by transaction type.
     */
    public function scopeByTxnType($query, int $txnTypeId)
    {
        return $query->where('loan_txn_type_id', $txnTypeId);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('txn_date', [$startDate, $endDate]);
    }
}
