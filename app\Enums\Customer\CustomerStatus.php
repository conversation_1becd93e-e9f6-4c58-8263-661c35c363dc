<?php

namespace App\Enums\Customer;

use App\Traits\EnumTrait;

enum CustomerStatus: int
{
    use EnumTrait;

    case ACTIVE = 0;
    case INACTIVE = 1;

    /**
     * Get all enum values as an array for dropdown options
     */
    public static function options(): array
    {
        return [
            self::ACTIVE->value => 'Active',
            self::INACTIVE->value => 'Inactive',
        ];
    }
}
