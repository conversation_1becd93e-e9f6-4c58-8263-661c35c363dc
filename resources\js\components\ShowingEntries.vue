<script setup lang="ts">
interface Props {
    from: number;
    to: number;
    total: number;
    entityName?: string;
}

const props = withDefaults(defineProps<Props>(), {
    entityName: 'results',
});
</script>

<template>
    <div class="text-sm text-gray-700">
        Showing <span class="font-medium">{{ props.from }}</span> to <span class="font-medium">{{ props.to }}</span> of
        <span class="font-medium">{{ props.total }}</span> entries
    </div>
</template>
