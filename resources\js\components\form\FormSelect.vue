<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Multiselect from '@vueform/multiselect';
import '@vueform/multiselect/themes/default.css';
import { computed } from 'vue';

interface Option {
    id?: number;
    value: string | number;
    label: string;
    category?: string;
}

interface Props {
    id: string;
    label: string;
    modelValue: string | number | (string | number)[];
    options: Option[];
    placeholder?: string;
    required?: boolean;
    error?: string;
    class?: string;
    labelClass?: string;
    selectClass?: string;
    multiple?: boolean;
    useMultiselect?: boolean;
    remark?: string;
    searchStatus?: string | number;
    filterByCountry?: boolean;
    selectedCountryId?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: 'Select an option',
    required: false,
    class: 'gap-3',
    labelClass: '',
    selectClass: '',
    multiple: false,
    useMultiselect: false,
    remark: '',
    searchStatus: '',
    filterByCountry: false,
    selectedCountryId: undefined,
});

const emit = defineEmits(['update:modelValue']);

const handleChange = (value: string | number | (string | number)[]) => {
    emit('update:modelValue', value);
};

const filteredOptions = computed(() => {
    if (!props.filterByCountry) {
        return props.options;
    }

    if (!props.selectedCountryId) {
        return [];
    }

    // Malaysia (ID: 25) - return all options
    if (props.selectedCountryId === 25) {
        return props.options;
    }
    // Singapore (ID: 26) - return empty array
    else if (props.selectedCountryId === 26) {
        return [];
    }

    // For other countries, return all options
    return props.options;
});

const shouldShowSelect = computed(() => {
    if (!props.filterByCountry) return true;

    // Show when Malaysia is selected (ID: 25)
    if (props.selectedCountryId === 25) return true;

    // Hide when Singapore is selected (ID: 26) or no country selected
    return props.selectedCountryId !== 26 && props.selectedCountryId !== null && props.selectedCountryId !== undefined;
});
</script>

<template>
    <div :class="props.class">
        <Label :for="props.id" :class="props.labelClass">
            {{ props.label }}
            <RequiredIndicator v-if="props.required" />
        </Label>
        <div v-if="shouldShowSelect" :class="['relative', !props.filterByCountry && 'mt-1']">
            <template v-if="!props.useMultiselect && !props.multiple">
                <Select :model-value="props.modelValue" @update:model-value="handleChange">
                    <SelectTrigger class="w-full">
                        <SelectValue :placeholder="props.placeholder" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem v-for="option in filteredOptions" :key="option.id || option.value" :value="option.id || option.value">
                            {{ option.label || option.value }}
                        </SelectItem>
                    </SelectContent>
                </Select>
            </template>
            <template v-else>
                <Multiselect
                    :model-value="props.modelValue"
                    @update:model-value="handleChange"
                    :options="filteredOptions"
                    :placeholder="props.placeholder"
                    :multiple="props.multiple"
                    :searchable="true"
                    :close-on-select="!props.multiple"
                    :value-prop="filteredOptions.length > 0 && filteredOptions[0].id ? 'id' : 'value'"
                    label="label"
                />
            </template>
        </div>
        <div v-else-if="props.filterByCountry && !props.selectedCountryId" class="text-muted-foreground mt-1 text-sm">
            Please select a country first
        </div>
        <div v-else-if="props.filterByCountry && props.selectedCountryId === 26" class="text-muted-foreground mt-1 text-sm">
            No states available for Singapore
        </div>
        <div v-else-if="props.filterByCountry && filteredOptions.length === 0" class="text-muted-foreground mt-1 text-sm">
            No states available for selected country
        </div>
        <p v-if="props.remark !== ''" class="text-muted-foreground text-sm">
            {{ props.remark }}
        </p>
        <InputError class="mt-1" :message="props.error" />
    </div>
</template>

<style>
.multiselect {
    min-height: 34px;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 14px;
    border-color: #e5e5e5;
}

.multiselect.is-active {
    box-shadow: 0 0 0 2px hsl(var(--ring));
}

.multiselect-placeholder {
    color: #737373;
}

.multiselect-wrapper {
    min-height: 34px;
}

.multiselect-tags {
    margin: 0rem 0.05rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.2rem;
}

.multiselect-tag {
    background-color: #f9f9f9;
    color: #0a0a0a;
    margin: 0.125rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.1rem;
    border: 1px solid #e2e8f0;
    font-weight: unset;
}

.multiselect-tag-remove {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #999999;
}

/* Checkbox styling for options */
.multiselect-option {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    cursor: pointer;
    gap: 0.5rem;
}

.multiselect-option.is-selected {
    background-color: unset;
    color: var(--color-black);
}

.multiselect-option.is-selected.is-pointed {
    background-color: #f3f4f6;
    color: var(--color-black);
}

.multiselect-option::before {
    content: '';
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 1px solid #cbd5e1;
    border-radius: 4px;
    margin-right: 8px;
    background-color: white;
}

.multiselect-option.is-selected::before {
    background-color: #3b82f6;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white' width='18px' height='18px'%3E%3Cpath d='M0 0h24v24H0z' fill='none'/%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 12px;
    border-color: #3b82f6;
}

.multiselect-caret {
    width: 1rem;
    height: 1rem;
    background-color: #b9b9b9;
    -webkit-mask-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJtNiA5IDYgNiA2LTYiLz48L3N2Zz4=');
    mask-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cGF0aCBkPSJtNiA5IDYgNiA2LTYiLz48L3N2Zz4=');
}

.multiselect-caret.is-open {
    pointer-events: auto;
    transform: unset;
}
</style>
