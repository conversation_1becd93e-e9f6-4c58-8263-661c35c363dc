<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loan_bank_accounts', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Foreign key relationships
            $table->foreignId('loan_id')->constrained('loans')->cascadeOnDelete();

            // Selection references
            $table->foreignId('selection_bank_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('bank', 72)->nullable();
            $table->foreignId('selection_type_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('type', 72)->nullable();

            // Account details
            $table->string('account_no', 24)->nullable();
            $table->string('account_name')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_bank_accounts');
    }
};
