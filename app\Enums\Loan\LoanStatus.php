<?php

namespace App\Enums\Loan;

use App\Traits\EnumTrait;

enum LoanStatus: int
{
    use EnumTrait;

    case PENDING_PROCESS = 1;
    case PENDING_REVIEW = 2;
    case PENDING_APPROVAL = 3;
    case REJECTED = 4;
    case APPROVED = 5;
    case CUSTOMER_REJECTED = 6;
    case CUSTOMER_ACCEPTED = 7;
    case ON_GOING = 8;
    case ON_GOING_OVERDUE = 9;
    case COMPLETED = 10;
    case CANCELLED = 11;

    /**
     * Get all available statuses as an array
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return [
            self::PENDING_PROCESS->value => 'Pending Process',
            self::PENDING_REVIEW->value => 'Pending Review',
            self::PENDING_APPROVAL->value => 'Pending Approval',
            self::REJECTED->value => 'Rejected',
            self::APPROVED->value => 'Approved',
            self::CUSTOMER_REJECTED->value => 'Customer Rejected',
            self::CUSTOMER_ACCEPTED->value => 'Customer Accepted',
            self::ON_GOING->value => 'On Going',
            self::ON_GOING_OVERDUE->value => 'On Going (Overdue)',
            self::COMPLETED->value => 'Completed',
            self::CANCELLED->value => 'Cancelled',
        ];
    }

    /**
     * Get the display name for a status
     */
    public function label(): string
    {
        return match ($this) {
            self::PENDING_PROCESS->value => 'Pending Process',
            self::PENDING_REVIEW->value => 'Pending Review',
            self::PENDING_APPROVAL->value => 'Pending Approval',
            self::REJECTED->value => 'Rejected',
            self::APPROVED->value => 'Approved',
            self::CUSTOMER_REJECTED->value => 'Customer Rejected',
            self::CUSTOMER_ACCEPTED->value => 'Customer Accepted',
            self::ON_GOING->value => 'On Going',
            self::ON_GOING_OVERDUE->value => 'On Going (Overdue)',
            self::COMPLETED->value => 'Completed',
            self::CANCELLED->value => 'Cancelled',
        };
    }
}
