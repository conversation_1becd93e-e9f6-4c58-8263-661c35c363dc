<script setup lang="ts">
import { Button } from '@/components/ui/button';
import {
    Combobox,
    ComboboxAnchor,
    ComboboxEmpty,
    ComboboxGroup,
    ComboboxInput,
    ComboboxItem,
    ComboboxItemIndicator,
    ComboboxList,
    ComboboxTrigger,
} from '@/components/ui/combobox';
import { Check, ChevronsUpDown } from 'lucide-vue-next';
import { computed, ref, watch } from 'vue';

interface Item {
    value: string | number;
    label: string;
}

const props = defineProps<{
    items: Record<string, string> | string[];
    placeholder: string;
    searchPlaceholder?: string;
    emptyMessage?: string;
    width?: string;
    modelValue?: string | number;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: string | null];
}>();

const itemsList = computed<Item[]>(() => {
    if (Array.isArray(props.items)) {
        return [{ value: '', label: 'All' }, ...props.items.map((item) => ({ value: item, label: item }))];
    } else {
        return [{ value: '', label: 'All' }, ...Object.entries(props.items).map(([value, label]) => ({ value, label }))];
    }
});

const selectedItem = ref<Item | undefined>(itemsList.value.find((item) => item.value === props.modelValue));

watch(
    () => props.modelValue,
    (newValue) => {
        selectedItem.value = itemsList.value.find((item) => item.value === newValue);
    },
);

const handleChange = (item: Item | undefined) => {
    selectedItem.value = item;
    emit('update:modelValue', item?.value?.toString() || null);
};
</script>

<template>
    <Combobox v-model="selectedItem" by="label" @update:model-value="handleChange">
        <ComboboxAnchor as-child>
            <ComboboxTrigger as-child>
                <Button variant="outline" :class="`justify-between ${width || 'w-[180px]'}`">
                    {{ selectedItem?.label ?? placeholder }}
                    <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </ComboboxTrigger>
        </ComboboxAnchor>

        <ComboboxList>
            <div class="relative w-full max-w-sm items-center">
                <ComboboxInput
                    class="h-10 rounded-none border-0 border-b focus-visible:ring-0"
                    :placeholder="searchPlaceholder || `Search ${placeholder.toLowerCase()}...`"
                />
            </div>

            <ComboboxEmpty>
                {{ emptyMessage || `No ${placeholder.toLowerCase()} found.` }}
            </ComboboxEmpty>

            <ComboboxGroup>
                <ComboboxItem v-for="item in itemsList" :key="item.value" :value="item">
                    {{ item.label }}
                    <ComboboxItemIndicator>
                        <Check class="ml-auto h-4 w-4" />
                    </ComboboxItemIndicator>
                </ComboboxItem>
            </ComboboxGroup>
        </ComboboxList>
    </Combobox>
</template>
