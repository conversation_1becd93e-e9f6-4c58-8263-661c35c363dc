<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contacts', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Polymorphic relation
            $table->morphs('contactable');

            // Contact type and category
            $table->unsignedTinyInteger('category')->index();
            $table->foreignId('selection_type_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('type', 36)->nullable();

            // Contact information
            $table->foreignId('selection_country_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('country', 36)->nullable();
            $table->string('contact', 24)->nullable();

            // Contact preferences
            $table->boolean('is_primary')->default(false);
            $table->boolean('can_receive_sms')->default(false);

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
