<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Owner extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'ownerable_type',
        'ownerable_id',
        'name',
        'identity_no',
        'selection_type_id',
        'type',
        'selection_nationality_id',
        'nationality',
        'share_unit',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'selection_type_id' => 'integer',
            'selection_natioanality_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    public function ownerable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who created this contact.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this contact.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who deleted this contact.
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    public function selectionTypes(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_type_id');
    }

    public function selectionNationality(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_nationality_id');
    }
}
