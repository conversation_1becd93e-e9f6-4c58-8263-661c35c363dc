<?php

namespace Database\Seeders\User;

use App\Enums\User\UserStatus;
use App\Models\AdminProfile;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class TeamLoanOfficerUserSeeder extends Seeder
{
    public function run(): void
    {
        $team = Team::firstWhere('name', 'Company Team 2');
        if (! $team) {
            $this->command->error('Team not found. Please run TeamSeeder first.');

            return;
        }

        $teamLoanOfficerRole = Role::firstWhere('name', 'Team Loan Officer');
        if (! $teamLoanOfficerRole) {
            $this->command->error('Team Loan Officer role not found. Please run RoleSeeder first.');

            return;
        }

        $teamLoanOfficer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'TeamLoanOfficer',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'remember_token' => Str::random(10),
                'status' => UserStatus::ACTIVE,
            ]
        );

        if (! $teamLoanOfficer->hasRole($teamLoanOfficerRole)) {
            $teamLoanOfficer->assignRole($teamLoanOfficerRole);
        }

        if (! $teamLoanOfficer->adminProfiles()->exists()) {
            $superAdminId = User::where('email', '<EMAIL>')->value('id');

            $adminProfile = AdminProfile::create([
                'created_by' => $superAdminId,
                'updated_by' => $superAdminId,
            ]);

            $adminProfile->users()->attach($teamLoanOfficer->id);

            $adminProfile->teams()->syncWithoutDetaching([$team->id]);
        }

        $this->command->info('Team loan officer user created successfully.');
    }
}
