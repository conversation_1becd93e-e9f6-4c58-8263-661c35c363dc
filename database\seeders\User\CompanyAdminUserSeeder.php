<?php

namespace Database\Seeders\User;

use App\Enums\User\UserStatus;
use App\Models\AdminProfile;
use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class CompanyAdminUserSeeder extends Seeder
{
    public function run(): void
    {
        $company = Company::firstWhere('name', 'COMPANY001');
        if (! $company) {
            $this->command->error('Company not found. Please run CompanySeeder first.');

            return;
        }

        $companyAdminRole = Role::firstWhere('name', 'Company Admin');
        if (! $companyAdminRole) {
            $this->command->error('Company Admin role not found. Please run RoleSeeder first.');

            return;
        }

        $companyAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'username' => 'CompanyAdmin',
                'email_verified_at' => now(),
                'password' => Hash::make('password'),
                'remember_token' => Str::random(10),
                'status' => UserStatus::ACTIVE,
            ]
        );

        if (! $companyAdmin->hasRole($companyAdminRole)) {
            $companyAdmin->assignRole($companyAdminRole);
        }

        if (! $companyAdmin->adminProfiles()->exists()) {
            $superAdminId = User::where('email', '<EMAIL>')->value('id');

            $adminProfile = AdminProfile::create([
                'created_by' => $superAdminId,
                'updated_by' => $superAdminId,
            ]);

            $adminProfile->users()->attach($companyAdmin->id);

            $adminProfile->companies()->syncWithoutDetaching([$company->id]);
        }

        $this->command->info('Company admin user created successfully.');
    }
}
