<?php

namespace App\Http\Requests\Users;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules;

class UpdateUserRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = $this->route('user');
        $adminProfileId = $this->input('admin_profile_id');

        return [
            'username' => 'required|string|max:255|unique:users,username,'.$user->id,
            'email' => 'nullable|string|email|max:255|unique:users,email,'.$user->id,
            'password' => $this->password ? ['confirmed', Rules\Password::defaults()] : '',
            'role' => 'required|integer|exists:roles,id',
            'status' => 'required|integer',
            'headquarter_id' => 'nullable|exists:headquarters,id',
            'company_id' => 'nullable|exists:companies,id',
            'team_id' => 'nullable|exists:teams,id',
            'old_headquarter_id' => 'nullable|exists:headquarters,id',
            'old_company_id' => 'nullable|exists:companies,id',
            'old_team_id' => 'nullable|exists:teams,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'username.required' => 'The username is required.',
            'username.unique' => 'This username is already in use.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email is already in use.',
            'password.confirmed' => 'The password confirmation does not match.',
            'role.required' => 'Please select a role for this user.',
            'role.exists' => 'The selected role does not exist.',
            'status.required' => 'Please select a status for this user.',
            'headquarter_id.exists' => 'The selected headquarter does not exist.',
            'company_id.exists' => 'The selected company does not exist.',
            'team_id.exists' => 'The selected team does not exist.',
        ];
    }
}
