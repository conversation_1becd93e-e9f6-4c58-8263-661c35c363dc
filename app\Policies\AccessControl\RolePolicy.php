<?php

namespace App\Policies\AccessControl;

use App\Enums\AccessControl\PermissionName;
use App\Enums\AccessControl\RoleName;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Spatie\Permission\Models\Role;

class RolePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any roles.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_ROLES->value);
    }

    /**
     * Determine whether the user can view the role.
     *
     * @return bool
     */
    public function view(User $user, Role $role)
    {
        return $user->hasPermissionTo(PermissionName::READ_ROLES->value);
    }

    /**
     * Determine whether the user can create roles.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_ROLES->value);
    }

    /**
     * Determine whether the user can update the role.
     *
     * @return bool
     */
    public function update(User $user, Role $role)
    {
        // Prevent updating the Super Administrator role unless the user is a Super Administrator
        if ($role->name === RoleName::SUPER_ADMIN->value) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_ROLES->value);
    }

    /**
     * Determine whether the user can delete the role.
     *
     * @return bool
     */
    public function delete(User $user, Role $role)
    {
        // Prevent deleting the Super Administrator role
        if ($role->name === RoleName::SUPER_ADMIN->value) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::DELETE_ROLES->value);
    }
}
