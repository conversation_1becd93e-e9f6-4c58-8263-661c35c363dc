<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * LoanCustomerCompany model for managing loan customer company information
 */
class LoanCustomerCompany extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'loan_customer_profile_id',
        'current_paid_up_capital',
        'business_turnover',
        'business_turnover_date',
        'business_net_income',
        'business_net_income_date',
        'selection_nature_of_business_id',
        'nature_of_business',
        'selection_country_of_business_id',
        'country_of_business',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_customer_profile_id' => 'integer',
            'current_paid_up_capital' => 'decimal:2',
            'business_turnover' => 'decimal:2',
            'business_turnover_date' => 'datetime',
            'business_net_income' => 'decimal:2',
            'business_net_income_date' => 'datetime',
            'selection_nature_of_business_id' => 'integer',
            'selection_country_of_business_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan customer profile that owns this company information.
     */
    public function loanCustomerProfile(): BelongsTo
    {
        return $this->belongsTo(LoanCustomerProfile::class, 'loan_customer_profile_id');
    }

    /**
     * Get the owners for the loan customer company.
     */
    public function owners(): MorphMany
    {
        return $this->morphMany(Owner::class, 'ownerable');
    }

    /**
     * Get the selection for nature of business.
     */
    public function selectionNatureOfBusiness(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_nature_of_business_id');
    }

    /**
     * Get the selection for country of business.
     */
    public function selectionCountryOfBusiness(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_country_of_business_id');
    }
}
