<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loan_customer_companies', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Foreign key relationships
            $table->foreignId('loan_customer_profile_id')->constrained('loan_customer_profiles')->cascadeOnDelete();

            // Company Info
            $table->decimal('current_paid_up_capital', 24, 2)->nullable();
            $table->decimal('business_turnover', 24, 2)->nullable();
            $table->datetime('business_turnover_date')->nullable();
            $table->decimal('business_net_income', 24, 2)->nullable();
            $table->datetime('business_net_income_date')->nullable();

            // Selection references
            $table->foreignId('selection_nature_of_business_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('nature_of_business', 72)->nullable();
            $table->foreignId('selection_country_of_business_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('country_of_business', 36)->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_customer_companies');
    }
};
