<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface Props {
    open: boolean;
    title: string;
    description: string;
    confirmText?: string;
    cancelText?: string;
    confirmVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
    cancelVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
    processing?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    confirmVariant: 'destructive',
    cancelVariant: 'outline',
    processing: false,
});

const emit = defineEmits(['update:open', 'confirm', 'cancel']);

const handleConfirm = () => {
    emit('confirm');
};

const handleCancel = () => {
    emit('cancel');
    emit('update:open', false);
};

const updateOpen = (value: boolean) => {
    emit('update:open', value);
};
</script>

<template>
    <Dialog :open="props.open" @update:open="updateOpen">
        <DialogContent>
            <DialogHeader>
                <DialogTitle>{{ props.title }}</DialogTitle>
                <DialogDescription>{{ props.description }}</DialogDescription>
            </DialogHeader>

            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <Button :variant="props.confirmVariant" class="sm:ml-3" @click="handleConfirm" :disabled="props.processing">
                    {{ props.confirmText }}
                </Button>
                <Button :variant="props.cancelVariant" @click="handleCancel" :disabled="props.processing">
                    {{ props.cancelText }}
                </Button>
            </div>
        </DialogContent>
    </Dialog>
</template>
