<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Company, Headquarter, Role, Team } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { computed, watch } from 'vue';

const { formatEnumOptions, formatSelectionOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();
const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess, hasTeamAccess, primaryHeadquarterId, primaryCompanyId, primaryTeamId } = useAuth();

interface Props {
    roles: Role[];
    headquarters: Headquarter[];
    companies: Company[];
    teams: Team[];
    defaultStatus: number;
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    username: '',
    email: '',
    password: '',
    password_confirmation: '',
    role: null as number | null,
    status: props.defaultStatus,
    headquarter_id: hasHeadquarterAccess.value ? primaryHeadquarterId.value : null,
    company_id: hasCompanyAccess.value ? primaryCompanyId.value : null,
    team_id: !hasHeadquarterAccess && hasTeamAccess.value ? primaryTeamId.value : null,
});

const isRequiredHeadquarter = computed(() => {
    return form.role ? props.roles.find((r) => r.id === form.role)?.is_required_headquarter : true;
});

const isRequiredCompany = computed(() => {
    return form.role ? props.roles.find((r) => r.id === form.role)?.is_required_company : true;
});

const isRequiredTeam = computed(() => {
    return form.role ? props.roles.find((r) => r.id === form.role)?.is_required_team : true;
});

const isHeadquarterRole = computed(() => {
    return form.role ? props.roles.find((r) => r.id === form.role)?.is_headquarter : false;
});

const formFields = computed(() => {
    const fields = [];

    fields.push(
        {
            id: 'username',
            label: 'Username',
            type: 'input',
            required: true,
            placeholder: 'Username',
            error: form.errors.username,
            modelValue: form.username,
            updateValue: (value: string) => (form.username = value ? value.toUpperCase() : value),
        },
        {
            id: 'role',
            label: 'Role',
            type: 'select',
            required: true,
            placeholder: 'Role',
            error: form.errors.role,
            options: props.roles.map((role) => ({
                value: role.id,
                label: role.name,
            })),
            modelValue: form.role,
            updateValue: (value: number) => (form.role = value),
        },
        {
            id: 'password',
            label: 'Password',
            type: 'password',
            required: true,
            placeholder: 'Password',
            error: form.errors.password,
            modelValue: form.password,
            updateValue: (value: string) => (form.password = value),
        },
        {
            id: 'password_confirmation',
            label: 'Confirm Password',
            type: 'password',
            required: true,
            placeholder: 'Confirm Password',
            error: form.errors.password_confirmation,
            modelValue: form.password_confirmation,
            updateValue: (value: string) => (form.password_confirmation = value),
        },
    );

    if (!hasHeadquarterAccess.value && isRequiredHeadquarter.value) {
        fields.push({
            id: 'headquarter_id',
            label: 'Headquarter Name',
            type: 'select',
            required: isRequiredHeadquarter.value,
            placeholder: 'Headquarter Name',
            error: form.errors.headquarter_id,
            options: formatSelectionOptions(props.headquarters, 'id', 'display_name'),
            modelValue: form.headquarter_id,
            updateValue: (value: number) => (form.headquarter_id = value),
        });
    }

    if (!hasCompanyAccess.value && !isHeadquarterRole.value && form.role !== null && isRequiredCompany.value) {
        fields.push({
            id: 'company_id',
            label: 'Company Name',
            type: 'select',
            required: isRequiredCompany.value,
            placeholder: 'Company Name',
            error: form.errors.company_id,
            options: formatSelectionOptions(filteredCompanies.value, 'id', 'display_name'),
            modelValue: form.company_id,
            updateValue: (value: number) => (form.company_id = value),
        });
    }

    if (
        (!hasTeamAccess.value && form.role !== null && isRequiredTeam.value) ||
        (hasHeadquarterAccess.value && hasTeamAccess.value && !isHeadquarterRole.value && form.role !== null && isRequiredTeam.value)
    ) {
        fields.push({
            id: 'team_id',
            label: 'Team Name',
            type: 'select',
            required: isRequiredTeam.value,
            placeholder: 'Team Name',
            error: form.errors.team_id,
            options: formatSelectionOptions(filteredTeams.value, 'id', 'name'),
            modelValue: form.team_id,
            updateValue: (value: number) => (form.team_id = value),
        });
    }

    fields.push(
        {
            id: 'email',
            label: 'Email',
            type: 'input',
            required: false,
            placeholder: 'Email',
            error: form.errors.email,
            modelValue: form.email,
            updateValue: (value: string) => (form.email = value),
        },
        {
            id: 'status',
            label: 'Status',
            type: 'select',
            required: true,
            placeholder: 'Status',
            error: form.errors.status,
            options: formatEnumOptions(props.statuses),
            modelValue: form.status,
            updateValue: (value: number) => (form.status = value),
        },
    );

    return fields;
});

const filteredCompanies = computed(() => {
    if (!form.headquarter_id) return [];

    return props.companies.filter((company) => company.headquarter_id === form.headquarter_id && company.is_headquarter === isHeadquarterRole.value);
});

const filteredTeams = computed(() => {
    if (!form.company_id) return [];
    return props.teams.filter((team) => team.company_id === form.company_id);
});

watch(
    () => form.headquarter_id,
    () => {
        form.company_id = null;
        form.team_id = null;

        if (isHeadquarterRole.value && form.headquarter_id) {
            const headquarterCompany = props.companies.find((company) => company.headquarter_id === form.headquarter_id && company.is_headquarter);

            if (headquarterCompany && isRequiredCompany.value) {
                form.company_id = headquarterCompany.id;
            }
        }
    },
);

watch(
    () => form.company_id,
    () => {
        form.team_id = null;
    },
);

watch(
    () => form.role,
    () => {
        if (isHeadquarterRole.value && form.headquarter_id) {
            const headquarterCompany = props.companies.find((company) => company.headquarter_id === form.headquarter_id && company.is_headquarter);

            if (headquarterCompany && isRequiredCompany.value) {
                form.company_id = headquarterCompany.id;
            }
        } else {
            form.company_id = hasCompanyAccess.value ? primaryCompanyId.value : null;
            form.team_id = !hasHeadquarterAccess && hasTeamAccess.value ? primaryTeamId.value : null;
        }
    },
);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('users.store'),
            entityName: 'user',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Create User" />
        <div class="px-4 py-3">
            <Heading title="User" pageNumber="P000014" description="Create a new user record" />

            <AppCard title="Add New User" :form="form" backRoute="users.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                                :class="field.class"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
