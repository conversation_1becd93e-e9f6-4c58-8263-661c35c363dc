<?php

namespace App\Providers;

use App\Enums\AccessControl\RoleName;
use App\Models\AgentOutcome;
use App\Models\AgentOutcomeType;
use App\Models\AgentProfile;
use App\Models\Collateral;
use App\Models\Company;
use App\Models\CustomerProfile;
use App\Models\Headquarter;
use App\Models\Loan;
use App\Models\Locale;
use App\Models\Selection;
use App\Models\Team;
use App\Models\User;
use App\Policies\AccessControl\PermissionPolicy;
use App\Policies\AccessControl\RolePolicy;
use App\Policies\Agents\AgentOutcomePolicy;
use App\Policies\Agents\AgentOutcomeTypePolicy;
use App\Policies\Agents\AgentPolicy;
use App\Policies\Audits\AuditPolicy;
use App\Policies\Collaterals\CollateralPolicy;
use App\Policies\Companies\CompanyPolicy;
use App\Policies\Companies\HeadquarterPolicy;
use App\Policies\Customers\CustomerPolicy;
use App\Policies\Loans\LoanPolicy;
use App\Policies\Locales\LocalePolicy;
use App\Policies\Selections\SelectionPolicy;
use App\Policies\Teams\TeamPolicy;
use App\Policies\Users\UserPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use OwenIt\Auditing\Models\Audit;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Role::class => RolePolicy::class,
        Permission::class => PermissionPolicy::class,
        User::class => UserPolicy::class,
        Team::class => TeamPolicy::class,
        Audit::class => AuditPolicy::class,
        Locale::class => LocalePolicy::class,
        Selection::class => SelectionPolicy::class,
        Company::class => CompanyPolicy::class,
        Headquarter::class => HeadquarterPolicy::class,
        Collateral::class => CollateralPolicy::class,
        CustomerProfile::class => CustomerPolicy::class,
        Loan::class => LoanPolicy::class,
        AgentProfile::class => AgentPolicy::class,
        AgentOutcome::class => AgentOutcomePolicy::class,
        AgentOutcomeType::class => AgentOutcomeTypePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Implicitly grant "Super Administrator" role all permissions
        // This works in the app by using gate-related functions like auth()->user->can() and @can()
        Gate::before(function (User $user, string $ability) {
            return $user->hasRole(RoleName::SUPER_ADMIN) ? true : null;
        });
    }
}
