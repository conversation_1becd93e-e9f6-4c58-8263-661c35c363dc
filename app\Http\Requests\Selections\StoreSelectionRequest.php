<?php

namespace App\Http\Requests\Selections;

use App\Enums\Selection\SelectionStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class StoreSelectionRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'category' => ['required', 'string', 'max:32'],
            'value' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'sort_order' => ['nullable', 'integer'],
            'status' => ['nullable', new Enum(SelectionStatus::class)],
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'sort_order' => $this->input('sort_order', 100),
        ]);
    }
}
