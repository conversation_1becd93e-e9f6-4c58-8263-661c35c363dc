<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * LoanCustomerEmployment model for managing loan customer employment information
 */
class LoanCustomerEmployment extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'loan_customer_profile_id',
        'employer_name',
        'length_service_year',
        'length_service_month',
        'job_position',
        'selection_terms_id',
        'terms',
        'selection_occupation_id',
        'occupation',
        'selection_business_category_id',
        'business_category',
        'gross_income',
        'net_income',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_customer_profile_id' => 'integer',
            'length_service_year' => 'integer',
            'length_service_month' => 'integer',
            'selection_terms_id' => 'integer',
            'selection_occupation_id' => 'integer',
            'selection_business_category_id' => 'integer',
            'gross_income' => 'decimal:2',
            'net_income' => 'decimal:2',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan customer profile that owns this employment information.
     */
    public function loanCustomerProfile(): BelongsTo
    {
        return $this->belongsTo(LoanCustomerProfile::class, 'loan_customer_profile_id');
    }

    /**
     * Get the address for the loan customer employment.
     */
    public function address(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    /**
     * Get the contacts for the loan customer employment.
     */
    public function contacts(): MorphMany
    {
        return $this->morphMany(Contact::class, 'contactable');
    }

    /**
     * Get the selection for terms.
     */
    public function selectionTerms(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_terms_id');
    }

    /**
     * Get the selection for occupation.
     */
    public function selectionOccupation(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_occupation_id');
    }

    /**
     * Get the selection for business category.
     */
    public function selectionBusinessCategory(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_business_category_id');
    }
}
