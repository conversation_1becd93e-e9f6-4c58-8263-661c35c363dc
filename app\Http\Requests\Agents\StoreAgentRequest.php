<?php

namespace App\Http\Requests\Agents;

use App\Enums\Agent\AgentStatus;
use App\Http\Requests\BaseRequest;
use App\Models\Company;
use Illuminate\Validation\Rules\Enum;

class StoreAgentRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'display_name' => ['required', 'string', 'max:255'],
            'email' => ['nullable', 'email', 'max:255', 'unique:agent_profiles,email'],
            'status' => ['required', new Enum(AgentStatus::class)],
            'remark' => ['nullable', 'string', 'max:1000'],
            'headquarter_id' => ['required', 'exists:headquarters,id'],
            'company_id' => [
                'required',
                'exists:companies,id',
                function ($attribute, $value, $fail) {
                    $company = Company::find($value);
                    if (! $company || $company->headquarter_id != $this->headquarter_id) {
                        $fail('The selected company does not belong to the selected headquarter.');
                    }
                },
            ],
        ];
    }
}
