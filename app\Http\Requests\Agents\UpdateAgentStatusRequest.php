<?php

namespace App\Http\Requests\Agents;

use App\Enums\Agent\AgentStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateAgentStatusRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => ['required', new Enum(AgentStatus::class)],
        ];
    }
}
