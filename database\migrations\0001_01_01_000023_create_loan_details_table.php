<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loan_details', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Foreign key relationships
            $table->foreignId('loan_id')->constrained('loans')->cascadeOnDelete();

            // Loan Details
            $table->foreignId('selection_mode_type_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('mode_type')->nullable();
            $table->decimal('loan_principle_amount', 24, 2)->nullable();
            $table->decimal('last_payment', 24, 2)->nullable();
            $table->decimal('instalment_amount', 24, 2)->nullable();
            $table->decimal('interest', 24, 2)->nullable();
            $table->integer('no_of_instalment')->nullable();
            $table->foreignId('selection_repayment_method_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('repayment_method')->nullable();
            $table->datetime('commencement_date')->nullable();
            $table->datetime('next_due_date')->nullable();
            $table->datetime('stamping_date')->nullable();

            // Fees
            $table->decimal('stamp_duty', 24, 2)->nullable();
            $table->decimal('attestation_fee', 24, 2)->nullable();
            $table->decimal('legal_fee', 24, 2)->nullable();
            $table->decimal('processing_fee', 24, 2)->nullable();
            $table->decimal('misc_charges', 24, 2)->nullable();
            $table->decimal('late_payment_charges', 24, 2)->nullable();

            // Others
            $table->decimal('rebate', 24, 2)->nullable();
            $table->decimal('loan_disbursement_amount', 24, 2)->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_details');
    }
};
