<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ArrowLeftIcon } from 'lucide-vue-next';
import { computed } from 'vue';

interface Audit {
    id: number;
    user: {
        id: number;
        username: string;
        email: string | null;
    } | null;
    event: string;
    auditable_type: string;
    auditable_id: number;
    model_name: string;
    old_values: Record<string, any> | null;
    new_values: Record<string, any> | null;
    url: string | null;
    ip_address: string | null;
    user_agent: string | null;
    tags: string | null;
    created_at: string;
}

const props = defineProps<{
    audit: Audit;
}>();

const breadcrumbs = [
    {
        title: 'Audit Logs',
        href: route('audits.index'),
    },
    {
        title: `Audit #${props.audit.id}`,
        href: route('audits.show', props.audit.id),
    },
];

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
};

const getEventClass = (event: string) => {
    switch (event) {
        case 'created':
            return 'bg-green-100 text-green-800 hover:bg-green-100';
        case 'updated':
            return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
        case 'deleted':
            return 'bg-red-100 text-red-800 hover:bg-red-100';
        case 'restored':
            return 'bg-amber-100 text-amber-800 hover:bg-amber-100';
        default:
            return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
};

const hasChanges = computed(() => {
    return (
        (props.audit.old_values && Object.keys(props.audit.old_values).length > 0) ||
        (props.audit.new_values && Object.keys(props.audit.new_values).length > 0)
    );
});

const getChangedFields = computed(() => {
    const fields = new Set<string>();

    if (props.audit.old_values) {
        Object.keys(props.audit.old_values).forEach((key) => fields.add(key));
    }

    if (props.audit.new_values) {
        Object.keys(props.audit.new_values).forEach((key) => fields.add(key));
    }

    return Array.from(fields);
});

const formatValue = (value: any) => {
    if (value === null || value === undefined) {
        return '<null>';
    }

    if (typeof value === 'object') {
        return JSON.stringify(value, null, 2);
    }

    return String(value);
};

const getBrowserInfo = (userAgent: string | null) => {
    if (!userAgent) return 'Unknown';

    // Very basic browser detection
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('MSIE') || userAgent.includes('Trident')) return 'Internet Explorer';

    return 'Unknown Browser';
};

const getOSInfo = (userAgent: string | null) => {
    if (!userAgent) return 'Unknown';

    // Very basic OS detection
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac OS')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';

    return 'Unknown OS';
};
</script>

<template>
    <Head :title="`Audit Log #${audit.id}`" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex flex-col gap-6 p-6">
            <!-- Header -->
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <Link :href="route('audits.index')">
                        <Button variant="outline" size="icon">
                            <ArrowLeftIcon class="h-4 w-4" />
                        </Button>
                    </Link>
                    <div>
                        <h2 class="text-2xl font-semibold tracking-tight">Audit Log Details</h2>
                        <p class="text-muted-foreground text-sm">Viewing audit record #{{ audit.id }}</p>
                    </div>
                </div>
                <Badge :class="getEventClass(audit.event)">
                    {{ audit.event.toUpperCase() }}
                </Badge>
            </div>

            <!-- Basic Information -->
            <Card>
                <CardHeader>
                    <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Model</h3>
                            <p class="mt-1">{{ audit.model_name }}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Model ID</h3>
                            <p class="mt-1">{{ audit.auditable_id }}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">User</h3>
                            <p class="mt-1">{{ audit.user ? audit.user.username : 'System' }}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Timestamp</h3>
                            <p class="mt-1">{{ formatDate(audit.created_at) }}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Changes -->
            <Card>
                <CardHeader>
                    <CardTitle>Changes</CardTitle>
                </CardHeader>
                <CardContent>
                    <div v-if="hasChanges">
                        <Tabs default-value="table" class="w-full">
                            <TabsList class="grid w-full grid-cols-2">
                                <TabsTrigger value="table">Table View</TabsTrigger>
                                <TabsTrigger value="json">JSON View</TabsTrigger>
                            </TabsList>
                            <TabsContent value="table">
                                <div class="mt-4 rounded-md border">
                                    <table class="w-full">
                                        <thead>
                                            <tr class="bg-muted/50 border-b">
                                                <th class="text-muted-foreground h-10 px-4 text-left align-middle font-medium">Field</th>
                                                <th class="text-muted-foreground h-10 px-4 text-left align-middle font-medium">Old Value</th>
                                                <th class="text-muted-foreground h-10 px-4 text-left align-middle font-medium">New Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="field in getChangedFields" :key="field" class="border-b">
                                                <td class="p-4 align-middle font-medium">{{ field }}</td>
                                                <td class="p-4 align-middle">
                                                    <pre class="text-sm whitespace-pre-wrap">{{ formatValue(audit.old_values?.[field]) }}</pre>
                                                </td>
                                                <td class="p-4 align-middle">
                                                    <pre class="text-sm whitespace-pre-wrap">{{ formatValue(audit.new_values?.[field]) }}</pre>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </TabsContent>
                            <TabsContent value="json">
                                <div class="mt-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <h3 class="mb-2 text-sm font-medium text-gray-500">Old Values</h3>
                                        <div class="bg-muted max-h-96 overflow-auto rounded-md p-4">
                                            <pre class="text-sm">{{ JSON.stringify(audit.old_values, null, 2) || 'No old values' }}</pre>
                                        </div>
                                    </div>
                                    <div>
                                        <h3 class="mb-2 text-sm font-medium text-gray-500">New Values</h3>
                                        <div class="bg-muted max-h-96 overflow-auto rounded-md p-4">
                                            <pre class="text-sm">{{ JSON.stringify(audit.new_values, null, 2) || 'No new values' }}</pre>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>
                        </Tabs>
                    </div>
                    <div v-else class="py-6 text-center text-gray-500">No changes recorded for this audit event.</div>
                </CardContent>
            </Card>

            <!-- Request Information -->
            <Card>
                <CardHeader>
                    <CardTitle>Request Information</CardTitle>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">URL</h3>
                            <p class="mt-1 break-all">{{ audit.url || 'Not available' }}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">IP Address</h3>
                            <p class="mt-1">{{ audit.ip_address || 'Not available' }}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Browser</h3>
                            <p class="mt-1">{{ getBrowserInfo(audit.user_agent) }}</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500">Operating System</h3>
                            <p class="mt-1">{{ getOSInfo(audit.user_agent) }}</p>
                        </div>
                    </div>
                    <div class="mt-4" v-if="audit.user_agent">
                        <h3 class="text-sm font-medium text-gray-500">User Agent</h3>
                        <p class="mt-1 text-xs break-all">{{ audit.user_agent }}</p>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
