<?php

namespace App\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

trait HasAuditFields
{
    use DateTimeConversion;

    /**
     * The attributes that should be cast to native types.
     */
    private function formatDate($value)
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value)) : null;
    }

    /**
     * The attributes that should be cast to native types.
     */
    protected function createdAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $this->formatDate($value),
        );
    }

    /**
     * The attributes that should be cast to native types.
     */
    protected function updatedAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $this->formatDate($value),
        );
    }

    /**
     * Get the user who created this model.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this model.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who deleted this model.
     */
    public function deletedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Scope to load minimal user information for audit fields.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithAuditUsers($query)
    {
        return $query->with(['createdBy:id,username', 'updatedBy:id,username', 'deletedBy:id,username']);
    }

    /**
     * Boot the trait and set up event listeners for audit fields.
     */
    public static function bootHasAuditFields(): void
    {
        static::creating(function ($model) {
            if (Auth::check() && empty($model->created_by)) {
                $model->created_by = Auth::id();
                $model->updated_by = Auth::id();
            }
        });

        static::updating(function ($model) {
            if (Auth::check()) {
                $model->updated_by = Auth::id();
            }
        });

        static::deleting(function ($model) {
            if (Auth::check() && method_exists($model, 'trashed')) {
                $model->deleted_by = Auth::id();
                $model->save();
            }
        });
    }
}
