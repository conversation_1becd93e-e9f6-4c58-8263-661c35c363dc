<?php

namespace App\Http\Requests\Teams;

use App\Enums\Team\TeamStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateTeamStatusRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => ['required', new Enum(TeamStatus::class)],
        ];
    }
}
