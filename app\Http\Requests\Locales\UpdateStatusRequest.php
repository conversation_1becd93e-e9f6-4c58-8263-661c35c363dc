<?php

namespace App\Http\Requests\Locales;

use App\Enums\Locale\LocaleStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateStatusRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => ['required', new Enum(LocaleStatus::class)],
        ];
    }
}
