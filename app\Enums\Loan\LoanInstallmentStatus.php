<?php

namespace App\Enums\Loan;

use App\Traits\EnumTrait;

enum LoanInstallmentStatus: int
{
    use EnumTrait;

    case UNPAID = 0;
    case PAID = 1;
    case OVERDUE = 2;
    case PARTIALLY_PAID = 3;

    /**
     * Get all available statuses as an array
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return [
            self::UNPAID->value => 'Unpaid',
            self::PAID->value => 'Paid',
            self::OVERDUE->value => 'Overdue',
            self::PARTIALLY_PAID->value => 'Partially Paid',
        ];
    }

    /**
     * Get the display name for a status
     */
    public function label(): string
    {
        return match ($this) {
            self::UNPAID => 'Unpaid',
            self::PAID => 'Paid',
            self::OVERDUE => 'Overdue',
            self::PARTIALLY_PAID => 'Partially Paid',
        };
    }
}
