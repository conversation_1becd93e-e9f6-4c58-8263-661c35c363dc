<?php

namespace App\Enums\Collateral;

use App\Traits\EnumTrait;

enum CollateralStatus: int
{
    use EnumTrait;

    case ACTIVE = 0;
    case INACTIVE = 1;

    /**
     * Get all available statuses as an array
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return [
            self::ACTIVE->value => 'Active',
            self::INACTIVE->value => 'Inactive',
        ];
    }

    /**
     * Get the display name for a status
     */
    public function label(): string
    {
        return match ($this) {
            self::ACTIVE => 'Active',
            self::INACTIVE => 'Inactive',
        };
    }
}
