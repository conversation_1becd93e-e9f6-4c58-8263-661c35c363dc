@import 'tailwindcss';

@import 'tw-animate-css';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';

@custom-variant dark (&:is(.dark *));

@theme inline {
    --font-sans: Roboto, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar-background);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
    --color-cobalt: var(--cobalt);
    --color-cobalt-hover: var(--cobalt-hover);
    --color-teal: var(--teal);
    --color-teal-hover: var(--teal-hover);
    --color-canary: var(--canary);
    --color-green: var(--green);
    --color-cement: var(--cement);
    --color-sort: var(--sort);
    --color-azure: var(--azure);
    --color-gainsboro: var(--gainsboro);
    --color-greenish: var(--greenish);
    --color-stone: var(--stone);
    --color-mist: var(--mist);
    --color-platinum: var(--platinum);
    --animate-accordion-down: accordion-down 0.2s ease-out;
    --animate-accordion-up: accordion-up 0.2s ease-out;
    --color-cloud: var(--cloud);
    --color-pale: var(--pale);
    --color-lavender: var(--lavender);
    --color-steel: var(--steel);
    --color-ocean: var(--ocean);
    --color-orange: var(--orange);
    --color-chrome: var(--chrome);
    --color-castleton: var(--castleton);
    --color-pink: var(--pink);
    --color-soften: var(--soften);
    --color-tomato: var(--tomato);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

@layer utilities {
    body,
    html {
        --font-sans: 'Roboto', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    }
}

:root {
    --background: hsla(227, 12%, 97%, 1);
    --foreground: hsl(0 0% 3.9%);
    --card: hsl(0 0% 100%);
    --card-foreground: hsl(0 0% 3.9%);
    --popover: hsl(0 0% 100%);
    --popover-foreground: hsl(0 0% 3.9%);
    --primary: hsl(0 0% 9%);
    --primary-foreground: hsl(0 0% 98%);
    --secondary: hsl(0 0% 92.1%);
    --secondary-foreground: hsl(0 0% 9%);
    --muted: hsl(0 0% 96.1%);
    --muted-foreground: hsl(0 0% 45.1%);
    --accent: hsl(0 0% 96.1%);
    --accent-foreground: hsl(0 0% 9%);
    --destructive: hsl(0 84.2% 60.2%);
    --destructive-foreground: hsl(0 0% 98%);
    --border: hsl(0 0% 92.8%);
    --border-foreground: hsl(0 0% 91%);
    --input: hsl(0 0% 89.8%);
    --ring: hsl(0 0% 3.9%);
    --chart-1: hsl(12 76% 61%);
    --chart-2: hsl(173 58% 39%);
    --chart-3: hsl(197 37% 24%);
    --chart-4: hsl(43 74% 66%);
    --chart-5: hsl(27 87% 67%);
    --radius: 0.5rem;
    --sidebar-background: hsla(210, 9%, 23%, 1);
    --sidebar-foreground: hsla(0, 0%, 100%, 1);
    --sidebar-primary: hsl(0 0% 10%);
    --sidebar-primary-foreground: hsl(0 0% 98%);
    --sidebar-accent: hsl(0 0% 94%);
    --sidebar-accent-foreground: hsl(0 0% 30%);
    --sidebar-border: hsla(0, 0%, 100%, 0.4);
    --sidebar-ring: hsl(217.2 91.2% 59.8%);
    --sidebar: hsl(0 0% 98%);
    --cobalt: hsl(216, 80%, 57%);
    --cobalt-hover: hsl(227, 76%, 41%, 1);
    --teal: hsla(188, 80%, 40%, 1);
    --teal-hover: rgb(18, 118, 133);
    --canary: hsla(44, 100%, 57%, 1);
    --green: hsla(158, 75%, 40%, 1);
    --cement: hsla(240, 6%, 94%, 1);
    --sort: hsla(0, 0%, 80%, 1);
    --azure: hsla(211, 100%, 50%, 1);
    --greenish: hsla(134, 61%, 41%, 1);
    --gainsboro: hsla(0, 0%, 87%, 1);
    --stone: hsla(0, 0%, 97%, 1);
    --platinum: hsla(0, 0%, 56%, 1);
    --mist: hsla(0, 0%, 70%, 1);
    --cloud: hsla(240, 13%, 97%, 1);
    --pale: hsla(220, 5%, 56%, 1);
    --lavender: hsla(231, 45%, 94%, 1);
    --steel: hsla(213, 12%, 71%, 1);
    --ocean: hsla(212, 100%, 67%, 1);
    --orange: hsla(26, 100%, 57%, 1);
    --chrome: hsla(213, 27%, 84%, 1);
    --castleton: hsla(180, 84%, 41%, 1);
    --pink: hsla(329, 96%, 66%, 1);
    --soften: hsla(234, 97%, 71%, 1);
    --tomato: hsla(8, 91%, 57%, 1);
}

.dark {
    --background: hsl(0 0% 3.9%);
    --foreground: hsl(0 0% 98%);
    --card: hsl(0 0% 3.9%);
    --card-foreground: hsl(0 0% 98%);
    --popover: hsl(0 0% 3.9%);
    --popover-foreground: 0 0% 98%;
    --primary: hsl(0 0% 98%);
    --primary-foreground: hsl(0 0% 9%);
    --secondary: hsl(0 0% 14.9%);
    --secondary-foreground: hsl(0 0% 98%);
    --muted: hsl(0 0% 16.08%);
    --muted-foreground: hsl(0 0% 63.9%);
    --accent: hsl(0 0% 14.9%);
    --accent-foreground: hsl(0 0% 98%);
    --destructive: hsl(0 84% 60%);
    --destructive-foreground: hsl(0 0% 98%);
    --border: hsl(0 0% 14.9%);
    --border-foreground: hsl(0 0% 15.9%);
    --input: hsl(0 0% 14.9%);
    --ring: hsl(0 0% 83.1%);
    --chart-1: hsl(220 70% 50%);
    --chart-2: hsl(160 60% 45%);
    --chart-3: hsl(30 80% 55%);
    --chart-4: hsl(280 65% 60%);
    --chart-5: hsl(340 75% 55%);
    --sidebar-background: hsl(0 0% 7%);
    --sidebar-foreground: hsl(0 0% 95.9%);
    --sidebar-primary: hsl(360, 100%, 100%);
    --sidebar-primary-foreground: hsl(0 0% 100%);
    --sidebar-accent: hsl(0 0% 15.9%);
    --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
    --sidebar-border: hsl(0 0% 15.9%);
    --sidebar-ring: hsl(217.2 91.2% 59.8%);
    --sidebar: hsl(240 5.9% 10%);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}

/*
  ---break---
*/

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
    }
}
