<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loan_guarantors', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Foreign key relationships
            $table->foreignId('loan_id')->constrained('loans')->cascadeOnDelete();

            // Guarantor information
            $table->string('name')->nullable();
            $table->string('identity_no', 20)->nullable();
            $table->integer('age')->nullable();
            $table->string('birth_date')->nullable();

            // Selection references
            $table->foreignId('selection_gender_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('gender', 36)->nullable();
            $table->foreignId('selection_relationship_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('relationship')->nullable();
            $table->foreignId('selection_race_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('race', 36)->nullable();
            $table->foreignId('selection_nationality_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('nationality', 72)->nullable();

            // Employment Info
            $table->string('employment_name')->nullable();
            $table->unsignedTinyInteger('length_service_year')->nullable();
            $table->unsignedTinyInteger('length_service_month')->nullable();
            $table->string('job_position')->nullable();

            // Selection references
            $table->foreignId('selection_terms_of_employment_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('terms_of_employment')->nullable();
            $table->foreignId('selection_occupation_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('occupation')->nullable();
            $table->foreignId('selection_business_classification_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('business_classification')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_guarantors');
    }
};
