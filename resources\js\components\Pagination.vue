<script setup lang="ts">
import { Button } from '@/components/ui/button';
import type { Link, PaginationProps } from '@/types/table';

const { links } = defineProps<PaginationProps>();

const emit = defineEmits<{
    (e: 'navigate', url: string): void;
}>();

const handleClick = (url: string | null) => {
    if (url) {
        emit('navigate', url);
    }
};

const parseLabel = (label: string) => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = label;
    return tempDiv.textContent || label;
};

const getDisplayLinks = (links: Link[]) => {
    const total = links.length;
    if (total <= 9) return links;

    const prev = links[0];
    const next = links[total - 1];
    const numbered = links.slice(1, total - 1); // Remove prev/next

    const currentLink = numbered.find((link) => link.active);
    const currentPage = currentLink ? parseInt(currentLink.label) : 1;
    const lastPage = parseInt(numbered[numbered.length - 1].label);

    const display: (Link | { label: string; url: null; active: false })[] = [];

    // Always show prev
    display.push(prev);

    // Always show page 1
    display.push(numbered.find((l) => parseInt(l.label) === 1)!);

    if (currentPage <= 4) {
        // First 4 pages
        for (let i = 2; i <= 5; i++) {
            const link = numbered.find((l) => parseInt(l.label) === i);
            if (link) display.push(link);
        }
        display.push({ label: '...', url: null, active: false });
    } else if (currentPage >= lastPage - 3) {
        // Last 4 pages
        display.push({ label: '...', url: null, active: false });
        for (let i = lastPage - 4; i < lastPage; i++) {
            const link = numbered.find((l) => parseInt(l.label) === i);
            if (link) display.push(link);
        }
    } else {
        // Middle pages
        display.push({ label: '...', url: null, active: false });
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
            const link = numbered.find((l) => parseInt(l.label) === i);
            if (link) display.push(link);
        }
        display.push({ label: '...', url: null, active: false });
    }

    // Always show last page
    display.push(numbered.find((l) => parseInt(l.label) === lastPage)!);
    display.push(next);

    return display;
};
</script>
<template>
    <div class="flex gap-1">
        <template v-for="(link, i) in getDisplayLinks(links)" :key="i">
            <Button
                v-if="link.url === null"
                variant="outline"
                size="sm"
                @click="() => handleClick(link.url)"
                :class="[
                    'px-3',
                    link.active ? 'bg-cobalt text-primary-foreground hover:bg-cobalt-hover hover:text-white' : 'bg-card hover:bg-background',
                ]"
            >
                {{ parseLabel(link.label) }}
            </Button>
            <Button
                v-else
                variant="outline"
                size="sm"
                @click="() => handleClick(link.url)"
                :disabled="!link.url"
                :class="[
                    'px-3',
                    link.active ? 'bg-cobalt text-primary-foreground hover:bg-cobalt-hover hover:text-white' : 'bg-card hover:bg-background',
                ]"
            >
                {{ parseLabel(link.label) }}
            </Button>
        </template>
    </div>
</template>
