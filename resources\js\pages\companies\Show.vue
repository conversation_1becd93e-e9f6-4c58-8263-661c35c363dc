<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import { Company } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';

interface Props {
    company: Company;
    logo: any;
}

const props = defineProps<Props>();
const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Company: ${props.company.display_name}`" />
        <div class="px-4 py-3">
            <Heading title="Company" pageNumber="P000008" description="View details of the company record" />

            <AppCard title="View Company" backRoute="companies.index" :form="form" :itemId="props.company.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Headquarter Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.company.headquarter?.display_name || '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Company Code</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.company.code }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Company Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.company.display_name }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">New Business Registration No.</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.company.business_registration_no || '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Old Business Registration No.</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.company.old_business_registration_no || '-' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Status</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.company.status === 0 ? 'Active' : 'Inactive' }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 items-center py-3">
                        <Label class="font-medium">Logo</Label>
                        <dd class="col-span-2 flex justify-end text-sm">
                            <div v-if="props.logo" class="card rounded-sm border p-1">
                                <img :src="props.logo" alt="Logo preview" class="h-[74px] w-[74px] rounded object-contain" />
                            </div>
                        </dd>
                    </div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
