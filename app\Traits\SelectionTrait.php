<?php

namespace App\Traits;

use App\Models\Selection;

trait SelectionTrait
{
    /**
     * Get selections by category
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getSelectionsByCategory(string $category)
    {
        return Selection::getByCategory($category);
    }

    /**
     * Get selections by multiple categories
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getSelectionsByCategories(array $categories)
    {
        return Selection::getByCategories($categories);
    }

    /**
     * Get selections as array of options for dropdown
     */
    protected function getSelectionsOptionsForCategory(string $category): array
    {
        return Selection::getOptionsForCategory($category);
    }

    /**
     * Get selection by ID
     *
     * @return Selection|null
     */
    protected function getSelectionById(int $id)
    {
        return Selection::find($id);
    }

    /**
     * Get selection value by ID
     */
    protected function getSelectionValueById(int $id): ?string
    {
        $selection = $this->getSelectionById($id);

        return $selection ? $selection->value : null;
    }
}
