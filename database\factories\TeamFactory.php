<?php

namespace Database\Factories;

use App\Enums\Team\TeamStatus;
use App\Models\Company;
use App\Models\Headquarter;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Team>
 */
class TeamFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Team::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'headquarter_id' => Headquarter::factory(),
            'company_id' => Company::factory(),
            'code' => 'TM'.strtoupper(Str::random(6)),
            'name' => fake()->company().' Team',
            'contact_no' => fake()->optional(0.8)->phoneNumber(),
            'status' => TeamStatus::ACTIVE,
            'remark' => fake()->optional(0.7)->sentence(),
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Indicate that the team is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TeamStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the team is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => TeamStatus::INACTIVE,
        ]);
    }
}
