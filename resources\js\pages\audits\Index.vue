<script setup lang="ts">
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
    Combobox,
    ComboboxAnchor,
    ComboboxEmpty,
    ComboboxGroup,
    ComboboxInput,
    ComboboxItem,
    ComboboxItemIndicator,
    ComboboxList,
    ComboboxTrigger,
} from '@/components/ui/combobox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { format } from 'date-fns';
import { CalendarIcon, Check, ChevronsUpDown, MoreHorizontal } from 'lucide-vue-next';
import { ref, watch } from 'vue';

interface Audit {
    id: number;
    user: {
        id: number;
        username: string;
        email: string | null;
    } | null;
    event: string;
    auditable_type: string;
    auditable_id: number;
    model_name: string;
    old_values: Record<string, any> | null;
    new_values: Record<string, any> | null;
    url: string | null;
    ip_address: string | null;
    user_agent: string | null;
    created_at: string;
}

interface AuditableType {
    value: string;
    label: string;
}

interface Event {
    value: string;
    label: string;
}

interface Props {
    audits: {
        data: Audit[];
        links: any[];
        from: number;
        to: number;
        total: number;
        meta: {
            current_page: number;
            from: number;
            last_page: number;
            per_page: number;
            to: number;
            total: number;
        };
    };
    auditableTypes: AuditableType[];
    events: Event[];
    filters: {
        event?: string;
        user_id?: number;
        auditable_type?: string;
        from_date?: string;
        to_date?: string;
        per_page?: number;
        sort_field?: string;
        sort_direction?: 'asc' | 'desc';
    };
}

const props = defineProps<Props>();
const form = useForm({
    event: props.filters.event || 'all',
    auditable_type: props.filters.auditable_type || 'all',
    from_date: props.filters.from_date || '',
    to_date: props.filters.to_date || '',
    per_page: props.filters.per_page || 10,
    sort_field: props.filters.sort_field || 'created_at',
    sort_direction: props.filters.sort_direction || 'desc',
});

const fromDate = ref(props.filters.from_date ? new Date(props.filters.from_date) : null);
const toDate = ref(props.filters.to_date ? new Date(props.filters.to_date) : null);

// Update form when date pickers change
watch(fromDate, (newValue) => {
    form.from_date = newValue ? format(newValue, 'yyyy-MM-dd') : '';
});

watch(toDate, (newValue) => {
    form.to_date = newValue ? format(newValue, 'yyyy-MM-dd') : '';
});

const applyFilters = () => {
    // Convert 'all' to empty string for the backend
    const params = {
        ...form,
        event: form.event === 'all' ? '' : form.event,
        auditable_type: form.auditable_type === 'all' ? '' : form.auditable_type,
    };

    form.get(route('audits.index', params), {
        preserveState: true,
        preserveScroll: true,
        replace: true,
    });
};

const resetFilters = () => {
    form.event = '';
    form.auditable_type = '';
    form.from_date = '';
    form.to_date = '';
    fromDate.value = null;
    toDate.value = null;

    form.get(route('audits.index'), {
        preserveState: true,
        preserveScroll: true,
        replace: true,
    });
};

const handlePerPageChange = (value: number) => {
    form.per_page = value;
    applyFilters();
};

// Watch for changes to per_page
watch(
    () => form.per_page,
    (newValue) => {
        if (newValue) {
            handlePerPageChange(newValue);
        }
    },
);

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';
    form.sort_field = field;
    form.sort_direction = direction;
    applyFilters();
};

const getSortIcon = (field: string) => {
    if (props.filters.sort_field !== field) return null;
    return props.filters.sort_direction === 'asc' ? '↑' : '↓';
};

const getEventClass = (event: string) => {
    switch (event) {
        case 'created':
            return 'bg-green-50 text-green-700';
        case 'updated':
            return 'bg-blue-50 text-blue-700';
        case 'deleted':
            return 'bg-red-50 text-red-700';
        case 'restored':
            return 'bg-amber-50 text-amber-700';
        default:
            return 'bg-gray-50 text-gray-700';
    }
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
};

const handlePaginate = (url: string) => {
    form.get(url);
};
</script>

<template>
    <AppLayout>
        <Head title="Audit Logs" />

        <div class="px-4 py-6">
            <Heading title="Audit Logs" description="View system audit trail and activity history" />

            <!-- Filters -->
            <div class="mb-6 rounded-md border bg-white p-4 shadow-sm">
                <h3 class="mb-4 text-lg font-medium">Filters</h3>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <!-- Event Type Filter -->
                    <div>
                        <label class="mb-1 block text-sm font-medium text-gray-700">Event Type</label>
                        <Combobox v-model="form.event" by="value">
                            <ComboboxAnchor as-child>
                                <ComboboxTrigger as-child>
                                    <Button variant="outline" class="w-full justify-between">
                                        {{ form.event ? events.find((e) => e.value === form.event)?.label : 'All Events' }}
                                        <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                    </Button>
                                </ComboboxTrigger>
                            </ComboboxAnchor>
                            <ComboboxList>
                                <div class="relative w-full max-w-sm items-center">
                                    <ComboboxInput class="h-10 rounded-none border-0 border-b focus-visible:ring-0" placeholder="Search events..." />
                                </div>
                                <ComboboxEmpty>No event found.</ComboboxEmpty>
                                <ComboboxGroup>
                                    <ComboboxItem value="all">All Events</ComboboxItem>
                                    <ComboboxItem v-for="event in events" :key="event.value" :value="event.value">
                                        {{ event.label }}
                                        <ComboboxItemIndicator>
                                            <Check class="ml-auto h-4 w-4" />
                                        </ComboboxItemIndicator>
                                    </ComboboxItem>
                                </ComboboxGroup>
                            </ComboboxList>
                        </Combobox>
                    </div>

                    <!-- Model Type Filter -->
                    <div>
                        <label class="mb-1 block text-sm font-medium text-gray-700">Model Type</label>
                        <Combobox v-model="form.auditable_type" by="value">
                            <ComboboxAnchor as-child>
                                <ComboboxTrigger as-child>
                                    <Button variant="outline" class="w-full justify-between">
                                        {{ form.auditable_type ? auditableTypes.find((t) => t.value === form.auditable_type)?.label : 'All Models' }}
                                        <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                    </Button>
                                </ComboboxTrigger>
                            </ComboboxAnchor>
                            <ComboboxList>
                                <div class="relative w-full max-w-sm items-center">
                                    <ComboboxInput class="h-10 rounded-none border-0 border-b focus-visible:ring-0" placeholder="Search models..." />
                                </div>
                                <ComboboxEmpty>No model found.</ComboboxEmpty>
                                <ComboboxGroup>
                                    <ComboboxItem value="all">All Models</ComboboxItem>
                                    <ComboboxItem v-for="type in auditableTypes" :key="type.value" :value="type.value">
                                        {{ type.label }}
                                        <ComboboxItemIndicator>
                                            <Check class="ml-auto h-4 w-4" />
                                        </ComboboxItemIndicator>
                                    </ComboboxItem>
                                </ComboboxGroup>
                            </ComboboxList>
                        </Combobox>
                    </div>

                    <!-- From Date Filter -->
                    <div>
                        <label class="mb-1 block text-sm font-medium text-gray-700">From Date</label>
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button
                                    variant="outline"
                                    class="w-full justify-start text-left font-normal"
                                    :class="{ 'text-muted-foreground': !fromDate }"
                                >
                                    <CalendarIcon class="mr-2 h-4 w-4" />
                                    {{ fromDate ? format(fromDate, 'PPP') : 'Select date' }}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent class="w-auto p-0">
                                <Calendar v-model="fromDate" />
                            </PopoverContent>
                        </Popover>
                    </div>

                    <!-- To Date Filter -->
                    <div>
                        <label class="mb-1 block text-sm font-medium text-gray-700">To Date</label>
                        <Popover>
                            <PopoverTrigger asChild>
                                <Button
                                    variant="outline"
                                    class="w-full justify-start text-left font-normal"
                                    :class="{ 'text-muted-foreground': !toDate }"
                                >
                                    <CalendarIcon class="mr-2 h-4 w-4" />
                                    {{ toDate ? format(toDate, 'PPP') : 'Select date' }}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent class="w-auto p-0">
                                <Calendar v-model="toDate" />
                            </PopoverContent>
                        </Popover>
                    </div>
                </div>

                <div class="mt-4 flex justify-end space-x-2">
                    <Button variant="outline" @click="resetFilters">Reset</Button>
                    <Button @click="applyFilters">Apply Filters</Button>
                </div>
            </div>

            <!-- Per Page Selector -->
            <div class="mb-4 flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <Combobox v-model="form.per_page" by="value">
                        <ComboboxAnchor as-child>
                            <ComboboxTrigger as-child>
                                <Button variant="outline" class="w-[120px] justify-between">
                                    {{ form.per_page }} per page
                                    <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                            </ComboboxTrigger>
                        </ComboboxAnchor>
                        <ComboboxList>
                            <ComboboxGroup>
                                <ComboboxItem :value="10">10 per page</ComboboxItem>
                                <ComboboxItem :value="25">25 per page</ComboboxItem>
                                <ComboboxItem :value="50">50 per page</ComboboxItem>
                                <ComboboxItem :value="100">100 per page</ComboboxItem>
                            </ComboboxGroup>
                        </ComboboxList>
                    </Combobox>
                </div>
            </div>

            <!-- Audit Table -->
            <div class="rounded-md border">
                <Table>
                    <TableCaption>A list of system audit logs.</TableCaption>
                    <TableHeader>
                        <TableRow>
                            <TableHead class="w-[80px]">
                                <button class="hover:text-primary flex items-center gap-1" @click="() => handleSort('id')">
                                    ID
                                    <span v-if="getSortIcon('id')">{{ getSortIcon('id') }}</span>
                                </button>
                            </TableHead>
                            <TableHead>
                                <button class="hover:text-primary flex items-center gap-1" @click="() => handleSort('event')">
                                    Event
                                    <span v-if="getSortIcon('event')">{{ getSortIcon('event') }}</span>
                                </button>
                            </TableHead>
                            <TableHead>
                                <button class="hover:text-primary flex items-center gap-1" @click="() => handleSort('auditable_type')">
                                    Model
                                    <span v-if="getSortIcon('auditable_type')">{{ getSortIcon('auditable_type') }}</span>
                                </button>
                            </TableHead>
                            <TableHead>User</TableHead>
                            <TableHead>
                                <button class="hover:text-primary flex items-center gap-1" @click="() => handleSort('created_at')">
                                    Timestamp
                                    <span v-if="getSortIcon('created_at')">{{ getSortIcon('created_at') }}</span>
                                </button>
                            </TableHead>
                            <TableHead class="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        <TableRow v-for="audit in audits.data" :key="audit.id">
                            <TableCell class="font-medium">{{ audit.id }}</TableCell>
                            <TableCell>
                                <span class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium" :class="getEventClass(audit.event)">
                                    {{ audit.event }}
                                </span>
                            </TableCell>
                            <TableCell>
                                <div class="flex flex-col">
                                    <span class="font-medium">{{ audit.model_name }}</span>
                                    <span class="text-xs text-gray-500">ID: {{ audit.auditable_id }}</span>
                                </div>
                            </TableCell>
                            <TableCell>
                                <div v-if="audit.user" class="flex flex-col">
                                    <span class="font-medium">{{ audit.user.username }}</span>
                                    <span class="text-xs text-gray-500">{{ audit.user.email }}</span>
                                </div>
                                <span v-else class="text-gray-500">System</span>
                            </TableCell>
                            <TableCell>{{ formatDate(audit.created_at) }}</TableCell>
                            <TableCell class="text-right">
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" class="h-8 w-8 p-0">
                                            <span class="sr-only">Open menu</span>
                                            <MoreHorizontal class="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem @click="() => form.get(route('audits.show', audit.id))"> View Details </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </TableCell>
                        </TableRow>
                        <TableRow v-if="!audits.data.length">
                            <TableCell :colspan="6" class="h-24 text-center"> No audit logs found. </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>

            <!-- Pagination -->
            <div class="mt-4 flex items-center justify-between">
                <div class="text-sm text-gray-500">Showing {{ audits.from }} to {{ audits.to }} of {{ audits.total }} audit logs</div>
                <div class="flex gap-1">
                    <Pagination :links="audits.links" @navigate="handlePaginate" />
                </div>
            </div>
        </div>
    </AppLayout>
</template>
