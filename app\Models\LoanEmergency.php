<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * LoanEmergency model for managing loan emergency contact information
 */
class LoanEmergency extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'loan_id',
        'name',
        'identity_no',
        'age',
        'birth_date',
        'employment_name',
        'selection_gender_id',
        'gender',
        'selection_relationship_id',
        'relationship',
        'selection_race_id',
        'race',
        'selection_nationality_id',
        'nationality',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_id' => 'integer',
            'age' => 'integer',
            'birth_date' => 'date',
            'selection_gender_id' => 'integer',
            'selection_relationship_id' => 'integer',
            'selection_race_id' => 'integer',
            'selection_nationality_id' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan that owns this emergency contact.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class, 'loan_id');
    }

    /**
     * Get the emergency details for this emergency contact.
     */
    public function loanEmergencyDetails(): HasMany
    {
        return $this->hasMany(LoanEmergencyDetail::class, 'loan_emergency_id');
    }

    /**
     * Get the selection gender associated with this emergency contact.
     */
    public function selectionGender(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_gender_id');
    }

    /**
     * Get the selection relationship associated with this emergency contact.
     */
    public function selectionRelationship(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_relationship_id');
    }

    /**
     * Get the selection race associated with this emergency contact.
     */
    public function selectionRace(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_race_id');
    }

    /**
     * Get the selection nationality associated with this emergency contact.
     */
    public function selectionNationality(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_nationality_id');
    }

    public function getBirthDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }
}
