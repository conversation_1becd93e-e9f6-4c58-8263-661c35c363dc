<?php

namespace App\Enums\Loan;

use App\Traits\EnumTrait;

enum LoanPaymentStatus: int
{
    use EnumTrait;

    case PENDING = 0;
    case PAID = 1;
    case CANCELLED = 2;
    case REFUNDED = 3;

    /**
     * Get all available payment statuses as an array
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return [
            self::PENDING->value => 'Pending',
            self::PAID->value => 'Paid',
            self::CANCELLED->value => 'Cancelled',
            self::REFUNDED->value => 'Refunded',
        ];
    }

    /**
     * Get the display name for a payment status
     */
    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::PAID => 'Paid',
            self::CANCELLED => 'Cancelled',
            self::REFUNDED => 'Refunded',
        };
    }

    /**
     * Get the description for a payment status
     */
    public function description(): string
    {
        return match ($this) {
            self::PENDING => 'Payment is pending processing',
            self::PAID => 'Payment has been successfully processed',
            self::CANCELLED => 'Payment has been cancelled',
            self::REFUNDED => 'Payment has been refunded',
        };
    }

    /**
     * Get the color class for UI display
     */
    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'text-yellow-600 bg-yellow-100',
            self::PAID => 'text-green-600 bg-green-100',
            self::CANCELLED => 'text-red-600 bg-red-100',
            self::REFUNDED => 'text-blue-600 bg-blue-100',
        };
    }

    /**
     * Check if the payment status is active/successful
     */
    public function isActive(): bool
    {
        return $this === self::PAID;
    }

    /**
     * Check if the payment status allows modifications
     */
    public function isModifiable(): bool
    {
        return in_array($this, [self::PENDING]);
    }
}
