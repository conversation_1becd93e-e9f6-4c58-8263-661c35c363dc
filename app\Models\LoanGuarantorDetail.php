<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * LoanGuarantorDetail model for managing loan guarantor detail information
 */
class LoanGuarantorDetail extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'loan_guarantor_id',
        'type',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_guarantor_id' => 'integer',
            'type' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan guarantor that owns this detail.
     */
    public function loanGuarantor(): BelongsTo
    {
        return $this->belongsTo(LoanGuarantor::class, 'loan_guarantor_id');
    }

    /**
     * Get all of the guarantor detail's address.
     */
    public function address(): MorphMany
    {
        return $this->morphMany(Address::class, 'addressable');
    }

    /**
     * Get all of the guarantor detail's contact.
     */
    public function contact(): MorphMany
    {
        return $this->morphMany(Contact::class, 'contactable');
    }
}
