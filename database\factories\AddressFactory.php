<?php

namespace Database\Factories;

use App\Models\Address;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Address>
 */
class AddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Address::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'addressable_type' => Company::class,
            'addressable_id' => Company::factory(),
            'category' => 'business',
            'selection_type_id' => null,
            'company_name' => fake()->company(),
            'line_1' => fake()->streetAddress(),
            'line_2' => fake()->optional(0.5)->secondaryAddress(),
            'postcode' => fake()->postcode(),
            'city' => fake()->city(),
            'selection_state_id' => null,
            'selection_country_id' => null,
            'is_primary' => true,
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Configure the model factory to associate with an existing company.
     */
    public function forCompany(Company $company): static
    {
        return $this->state(fn () => [
            'addressable_type' => Company::class,
            'addressable_id' => $company->id,
        ]);
    }

    /**
     * Configure the model factory to associate with a specific model.
     */
    public function forModel(string $modelType, int $modelId): static
    {
        return $this->state(fn () => [
            'addressable_type' => $modelType,
            'addressable_id' => $modelId,
        ]);
    }

    /**
     * Set the address as primary.
     */
    public function primary(): static
    {
        return $this->state(fn () => [
            'is_primary' => true,
        ]);
    }

    /**
     * Set the address as non-primary.
     */
    public function nonPrimary(): static
    {
        return $this->state(fn () => [
            'is_primary' => false,
        ]);
    }
}
