<?php

namespace App\Http\Resources\Collaterals;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CollateralResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $valuer = $this->valuers->firstWhere('is_primary', true);

        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'company' => $this->whenLoaded('company', fn () => [
                'id' => $this->company->id,
                'display_name' => $this->company->display_name,
                'headquarter' => $this->company->headquarter ? fn () => [
                    'id' => $this->company->headquarter->id,
                    'display_name' => $this->company->headquarter->display_name,
                ] : null,
            ]),
            'team' => $this->whenLoaded('team', fn () => [
                'id' => $this->team->id,
                'name' => $this->team->name,
            ]),
            'selection_type_id' => $this->selection_type_id,
            'selection_customer_type_id' => $this->selection_customer_type_id,
            'customer_type_selection' => $this->customerTypeSelection ? $this->customerTypeSelection->value : null,
            'type_selection' => $this->typeSelection ? $this->typeSelection->value : null,
            'name' => $this->name,
            'identity_no' => $this->identity_no,
            'status' => $this->status->value,
            'status_label' => $this->status->label(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
            'property' => $this->property ? [
                'address' => $this->property->address ? [
                    'line_1' => $this->property->address->line_1,
                ] : null,
            ] : null,
            'valuer' => $this->whenLoaded('valuers', fn () => $this->valuers->map(fn ($valuer) => [
                'valuer' => $valuer->valuer,
                'valuation_received_date' => $valuer->valuation_received_date,
                'land_search_received_date' => $valuer->land_search_received_date,
            ])),
        ];
    }
}
