<script setup lang="ts">
import Calendar from '@/components/calendar/Calendar.vue';
import FaIcon from '@/components/FaIcon.vue';
import FormInputCurrency from '@/components/form/FormInputCurrency.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ref } from 'vue';

interface Valuer {
    id?: number;
    valuer: string;
    valuation_amount: number;
    valuation_received_date: string | null;
    land_search_received_date: string | null;
    is_primary: boolean | null;
    _delete?: boolean;
}

const props = defineProps<{
    valuers: Valuer[];
    errors?: Record<string, string>;
    collateralSide?: boolean;
    ordinal: (n: number) => string;
    goBack?: () => void;
    goNext?: () => void;
}>();

const openAccordion = ref('0');

// Add new valuer
const addValuer = () => {
    props.valuers.push({
        valuation_amount: 0,
        valuer: '',
        valuation_received_date: null,
        land_search_received_date: null,
        is_primary: false,
    });
};

// Remove valuer
const removeValuer = (index: number) => {
    if (props.valuers[index].id) {
        props.valuers[index]._delete = true;
    } else {
        props.valuers.splice(index, 1);
    }
};
</script>

<template>
    <CardContent :class="['py-4', !collateralSide && 'px-0']">
        <div class="flex justify-between pb-3">
            <Label class="text-[20px]">Valuation</Label>
            <Button type="button" class="bg-teal hover:bg-teal-hover flex items-center gap-2" @click="addValuer">
                <FaIcon name="plus" />
                Add New
            </Button>
        </div>

        <Accordion type="single" class="w-full" collapsible v-model="openAccordion">
            <AccordionItem v-for="(valuer, index) in valuers" :key="index" :value="String(index)" class="mb-1">
                <Card v-if="!valuer._delete" class="gap-0 rounded-xs py-0">
                    <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                        <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                            <FaIcon name="plus" />
                        </span>
                        <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                            <FaIcon name="minus" />
                        </span>
                        <span class="flex-1 py-2 text-left font-medium"> {{ ordinal(index + 1) }} Valuation </span>
                        <template #icon>
                            <Button
                                type="button"
                                @click.stop="removeValuer(index)"
                                variant="destructive"
                                :hidden="index === 0"
                                class="flex items-center gap-1"
                            >
                                <FaIcon name="trash" />
                                Delete
                            </Button>
                        </template>
                    </AccordionTrigger>

                    <Separator />
                    <AccordionContent class="p-2">
                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div>
                                <Label :for="`valuers.${index}.valuer`" class="text-base"
                                    >Valuer
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`valuers.${index}.valuer`"
                                    v-model="valuer.valuer"
                                    :error="errors?.[`valuers.${index}.valuer`]"
                                    placeholder="Valuer"
                                />
                                <div v-if="errors?.[`valuers.${index}.valuer`]" class="mt-1 text-sm text-red-500">
                                    {{ errors?.[`valuers.${index}.valuer`] }}
                                </div>
                            </div>

                            <div>
                                <FormInputCurrency
                                    id="`valuers.${index}.valuation_amount`"
                                    label="Valuation (RM)"
                                    :required="true"
                                    v-model="valuer.valuation_amount"
                                    :error="errors?.[`valuers.${index}.valuation_amount`]"
                                    labelClass="text-base"
                                    placeholder="Valuation (RM)"
                                />
                            </div>

                            <div>
                                <Label :for="`valuers.${index}.valuation_received_date`" class="text-base"
                                    >Valuation Received Date
                                    <RequiredIndicator />
                                </Label>
                                <Calendar
                                    v-model="valuer.valuation_received_date"
                                    disableFuture
                                    :error="errors?.[`valuers.${index}.valuation_received_date`]"
                                    placeholderLabel="Valuation Received Date"
                                />
                                <div v-if="errors?.[`valuers.${index}.valuation_received_date`]" class="mt-1 text-sm text-red-500">
                                    {{ errors?.[`valuers.${index}.valuation_received_date`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`valuers.${index}.land_search_received_date`" class="text-base">Land Search Received Date</Label>
                                <Calendar
                                    v-model="valuer.land_search_received_date"
                                    disableFuture
                                    :error="errors?.[`valuers.${index}.land_search_received_date`]"
                                    placeholderLabel="Land Search Received Date"
                                />
                                <div v-if="errors?.[`valuers.${index}.land_search_received_date`]" class="mt-1 text-sm text-red-500">
                                    {{ errors?.[`valuers.${index}.land_search_received_date`] }}
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </Card>
            </AccordionItem>
        </Accordion>
    </CardContent>

    <CardFooter v-if="collateralSide" class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
        <Button variant="outline" @click="goBack" type="button" class="bg-card text-muted-foreground flex items-center gap-2">
            <FaIcon name="chevron-left" />
            Back
        </Button>
        <Button variant="outline" @click="goNext" type="button" class="bg-green flex items-center gap-2 text-white">
            Next
            <FaIcon name="chevron-right" />
        </Button>
    </CardFooter>
</template>
