<?php

namespace Database\Factories;

use App\Models\AdminProfile;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AdminProfile>
 */
class AdminProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AdminProfile::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'code' => 'ADM'.strtoupper(Str::random(6)),
            'contact_no' => fake()->optional(0.8)->phoneNumber(),
            'remark' => fake()->optional(0.7)->sentence(),
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }
}
