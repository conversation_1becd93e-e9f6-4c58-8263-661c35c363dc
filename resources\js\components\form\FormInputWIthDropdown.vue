<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import '@vueform/multiselect/themes/default.css';
import { PropType } from 'vue';

// Define props directly without assigning to a variable
defineProps({
    id: { type: String, required: true },
    label: { type: String, required: true },
    modelValue: { type: Object as PropType<{ select: number; input: string }>, required: true },
    placeholder: { type: String, default: '' },
    required: { type: Boolean, default: false },
    error: { type: String, default: undefined },
    labelClass: { type: String, default: '' },
    selectPosition: { type: String, default: 'left' },
    selectClass: { type: String, default: '' },
    remark: { type: String, default: '' },
    selectPlaceholder: { type: String, default: 'Select option' },
    inputPlaceholder: { type: String, default: 'Enter value' },
    options: { type: Array, default: () => [] },
});

// Define emits directly without assigning to a variable
defineEmits(['update:modelValue']);
</script>

<template>
    <div>
        <Label :for="id" :class="labelClass">
            {{ label }}
            <RequiredIndicator v-if="required" />
        </Label>

        <div class="mt-1 flex">
            <!-- Left positioned select -->
            <template v-if="selectPosition === 'left'">
                <Select :model-value="modelValue.select" @update:model-value="$emit('update:modelValue', { ...modelValue, select: $event })">
                    <SelectTrigger class="w-25 !rounded-r-none bg-gray-100">
                        <SelectValue :placeholder="selectPlaceholder" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem v-for="option in options" :key="option.value" :value="option.value">
                            {{ option.label }}
                        </SelectItem>
                    </SelectContent>
                </Select>
                <Input
                    :id="id"
                    type="text"
                    class="w-full !rounded-l-none"
                    :placeholder="inputPlaceholder"
                    :model-value="modelValue.input"
                    @update:model-value="$emit('update:modelValue', { ...modelValue, input: $event })"
                />
            </template>

            <!-- Right positioned select -->
            <template v-else>
                <Input
                    :id="id"
                    class="w-full !rounded-r-none"
                    :placeholder="inputPlaceholder"
                    :model-value="modelValue.input"
                    @update:model-value="$emit('update:modelValue', { ...modelValue, input: $event })"
                />
                <Select :model-value="modelValue.select" @update:model-value="$emit('update:modelValue', { ...modelValue, select: $event })">
                    <SelectTrigger :class="cn('w-45 !rounded-l-none bg-gray-100', selectClass)">
                        <SelectValue :placeholder="selectPlaceholder" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem v-for="option in options" :key="option.value" :value="option.value">
                            {{ option.label }}
                        </SelectItem>
                    </SelectContent>
                </Select>
            </template>
        </div>

        <p v-if="remark" class="text-muted-foreground text-sm">{{ remark }}</p>
        <InputError class="mt-1" :message="error" />
    </div>
</template>
