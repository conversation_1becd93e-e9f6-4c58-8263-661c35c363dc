<?php

use App\Enums\Company\CompanyStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('headquarter_id')->constrained('headquarters')->onDelete('cascade');
            $table->string('code', 36)->unique();

            // Company information
            $table->string('name');
            $table->string('display_name');
            $table->string('business_registration_no', 100)->nullable();
            $table->string('old_business_registration_no', 100)->nullable();
            $table->text('logo')->nullable();
            $table->boolean('is_headquarter')->default(false);
            $table->unsignedTinyInteger('status')->default(CompanyStatus::ACTIVE->value);

            // Additional information
            $table->text('remark')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();

            // Unique constraints
            $table->unique(['headquarter_id', 'name']);
        });

        Schema::create('companyables', function (Blueprint $table) {
            $table->foreignId('company_id')->constrained('companies')->onDelete('cascade');
            $table->morphs('companyable');
            $table->primary(['company_id', 'companyable_id', 'companyable_type'], 'companyables_primary');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companyables');
        Schema::dropIfExists('companies');
    }
};
