<?php

use App\Models\User;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Support\Facades\Notification;

// test('reset password link screen can be rendered', function () {
//     $response = $this->get('/forgot-password');

//     $response->assertStatus(200);
// });

// test('reset password link can be requested with email', function () {
//     Notification::fake();

//     $user = User::factory()->create([
//         'email' => '<EMAIL>',
//     ]);

//     $this->post('/forgot-password', ['identifier' => $user->email]);

//     Notification::assertSentTo($user, ResetPassword::class);
// });

// test('reset password link can be requested with username', function () {
//     Notification::fake();

//     $user = User::factory()->create([
//         'username' => 'testuser123',
//     ]);

//     $this->post('/forgot-password', ['identifier' => $user->username]);

//     Notification::assertSentTo($user, ResetPassword::class);
// });

// test('reset password screen can be rendered', function () {
//     Notification::fake();

//     $user = User::factory()->create([
//         'email' => '<EMAIL>',
//     ]);

//     $this->post('/forgot-password', ['identifier' => $user->email]);

//     Notification::assertSentTo($user, ResetPassword::class, function ($notification) {
//         $response = $this->get('/reset-password/'.$notification->token);

//         $response->assertStatus(200);

//         return true;
//     });
// });

// test('password can be reset with valid token using email', function () {
//     Notification::fake();

//     $user = User::factory()->create([
//         'email' => '<EMAIL>',
//     ]);

//     $this->post('/forgot-password', ['identifier' => $user->email]);

//     Notification::assertSentTo($user, ResetPassword::class, function ($notification) use ($user) {
//         $response = $this->post('/reset-password', [
//             'token' => $notification->token,
//             'email' => $user->email,
//             'password' => 'password',
//             'password_confirmation' => 'password',
//         ]);

//         $response
//             ->assertSessionHasNoErrors()
//             ->assertRedirect(route('login'));

//         return true;
//     });
// });

// test('password can be reset with valid token using username', function () {
//     Notification::fake();

//     $user = User::factory()->create([
//         'username' => 'testuser456',
//     ]);

//     $this->post('/forgot-password', ['identifier' => $user->username]);

//     Notification::assertSentTo($user, ResetPassword::class, function ($notification) use ($user) {
//         $response = $this->post('/reset-password', [
//             'token' => $notification->token,
//             'username' => $user->username,
//             'password' => 'password',
//             'password_confirmation' => 'password',
//         ]);

//         $response
//             ->assertSessionHasNoErrors()
//             ->assertRedirect(route('login'));

//         return true;
//     });
// });
