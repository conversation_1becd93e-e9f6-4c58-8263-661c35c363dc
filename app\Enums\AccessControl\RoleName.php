<?php

namespace App\Enums\AccessControl;

enum RoleName: string
{
    case SUPER_ADMIN = 'Super Administrator';
    case HEADQUARTER_ADMIN = 'Headquarter Admin';
    case HEADQUARTER_LOAN_OFFICER = 'Headquarter Loan Officer';
    case HEADQUARTER_LOAN_REVIEWER = 'Headquarter Loan Reviewer';
    case HEADQUARTER_LOAN_APPROVER = 'Headquarter Loan Approver';
    case COMPANY_ADMIN = 'Company Admin';
    case TEAM_ADMIN = 'Team Admin';
    case TEAM_LOAN_OFFICER = 'Team Loan Officer';
    case TEAM_LOAN_REVIEWER = 'Team Loan Reviewer';
    case TEAM_LOAN_APPROVER = 'Team Loan Approver';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
