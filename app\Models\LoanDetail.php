<?php

namespace App\Models;

use App\Traits\HasLoanInstallments;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * LoanDetail model for managing loan detail information
 */
class LoanDetail extends BaseModel
{
    use HasFactory, HasLoanInstallments;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'loan_id',
        'selection_mode_type_id',
        'mode_type',
        'loan_principle_amount',
        'last_payment',
        'instalment_amount',
        'interest',
        'no_of_instalment',
        'selection_repayment_method_id',
        'repayment_method',
        'commencement_date',
        'next_due_date',
        'stamping_date',
        'stamp_duty',
        'attestation_fee',
        'legal_fee',
        'processing_fee',
        'misc_charges',
        'late_payment_charges',
        'rebate',
        'loan_disbursement_amount',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_id' => 'integer',
            'selection_mode_type_id' => 'integer',
            'loan_principle_amount' => 'decimal:2',
            'last_payment' => 'decimal:2',
            'instalment_amount' => 'decimal:2',
            'interest' => 'decimal:2',
            'no_of_instalment' => 'integer',
            'selection_repayment_method_id' => 'integer',
            'stamp_duty' => 'decimal:2',
            'attestation_fee' => 'decimal:2',
            'legal_fee' => 'decimal:2',
            'processing_fee' => 'decimal:2',
            'misc_charges' => 'decimal:2',
            'late_payment_charges' => 'decimal:2',
            'rebate' => 'decimal:2',
            'loan_disbursement_amount' => 'decimal:2',
            'commencement_date' => 'datetime',
            'next_due_date' => 'datetime',
            'stamping_date' => 'datetime',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    public function getCommencementDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }

    public function getNextDueDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }

    public function getStampingDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }

    /**
     * Get the loan that owns this detail.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class, 'loan_id');
    }

    /**
     * Get the selection for mode type.
     */
    public function selectionModeType(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_mode_type_id');
    }

    /**
     * Get the selection for repayment method.
     */
    public function selectionRepaymentMethod(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_repayment_method_id');
    }
}
