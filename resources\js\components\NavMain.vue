<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
    useSidebar,
} from '@/components/ui/sidebar';
import { type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

const page = usePage<SharedData>();
const { state } = useSidebar();

const props = defineProps<{
    items: {
        title: string;
        url: string;
        icon?: string;
        isActive?: boolean;
        items?: {
            isActive: boolean;
            title: string;
            url: string;
        }[];
    }[];
}>();

const itemsWithActive = computed(() => {
    return props.items.map((item) => {
        const hasActiveSubItem =
            item.items && item.items.length > 0
                ? item.items.some(
                      (subItem) => subItem.isActive || subItem.url === page.url || (subItem.url !== '#' && page.url.startsWith(subItem.url)),
                  )
                : false;

        return {
            ...item,
            isActive: item.isActive || hasActiveSubItem || (item.url !== '#' && page.url.startsWith(item.url)),
        };
    });
});
</script>

<template>
    <SidebarGroup>
        <SidebarGroupLabel>Menu</SidebarGroupLabel>
        <SidebarMenu>
            <Collapsible v-for="item in itemsWithActive" :key="item.title" as-child :default-open="item.isActive" class="group/collapsible">
                <SidebarMenuItem>
                    <template v-if="item.items && state === 'collapsed'">
                        <DropdownMenu>
                            <DropdownMenuTrigger as-child>
                                <SidebarMenuButton :tooltip="item.title">
                                    <FaIcon v-if="item.icon" :name="item.icon" />
                                    <span>{{ item.title }}</span>
                                </SidebarMenuButton>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent side="right" align="start" class="w-48">
                                <DropdownMenuItem v-for="subItem in item.items" :key="subItem.title" :as-child="true">
                                    <Link :href="subItem.url" class="w-full">
                                        <span>{{ subItem.title }}</span>
                                    </Link>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </template>
                    <template v-else>
                        <CollapsibleTrigger as-child>
                            <template v-if="item.items">
                                <SidebarMenuButton :tooltip="item.title">
                                    <FaIcon v-if="item.icon" :name="item.icon" />
                                    <span>{{ item.title }}</span>
                                    <FaIcon
                                        name="chevron-right"
                                        size="sm"
                                        class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
                                    />
                                </SidebarMenuButton>
                            </template>
                            <Link v-if="item.url !== '#'" :href="item.url">
                                <SidebarMenuButton :tooltip="item.title" :is-active="item.isActive">
                                    <FaIcon v-if="item.icon" :name="item.icon" />
                                    <span>{{ item.title }}</span>
                                </SidebarMenuButton>
                            </Link>
                        </CollapsibleTrigger>
                        <CollapsibleContent v-if="item.items">
                            <SidebarMenuSub>
                                <SidebarMenuSubItem v-for="subItem in item.items" :key="subItem.title">
                                    <SidebarMenuSubButton as-child :is-active="subItem.isActive">
                                        <Link :href="subItem.url">
                                            <span>{{ subItem.title }}</span>
                                        </Link>
                                    </SidebarMenuSubButton>
                                </SidebarMenuSubItem>
                            </SidebarMenuSub>
                        </CollapsibleContent>
                    </template>
                </SidebarMenuItem>
            </Collapsible>
        </SidebarMenu>
    </SidebarGroup>
</template>
