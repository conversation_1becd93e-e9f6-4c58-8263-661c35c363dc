<?php

namespace App\Traits;

trait EnumTrait
{
    /**
     * Get enum case by label (case-insensitive)
     *
     * @param  string  $label  The display label to search for
     * @return self|null The matching enum case or null if not found
     */
    public static function fromLabel(string $label): ?self
    {
        $normalizedLabel = strtolower(trim($label));

        return collect(self::cases())
            ->first(fn ($case) => strtolower($case->label()) === $normalizedLabel);
    }
}
