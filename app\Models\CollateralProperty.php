<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

/**
 * CollateralProperty model for managing property details of collaterals
 */
class CollateralProperty extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'collateral_id',
        'ownership_no',
        'lot_number',
        'selection_land_category_id',
        'land_category',
        'land_category_other',
        'selection_type_of_property_id',
        'type_of_property',
        'land_size',
        'selection_land_size_unit',
        'land_size_unit',
        'selection_land_status_id',
        'land_status',
        'city',
        'location',
        'district',
        'no_syit_piawai',
        'certified_plan_no',
        'built_up_area_of_property',
        'selection_built_up_area_unit',
        'built_up_area_unit',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'uuid' => 'string',
        'collateral_id' => 'integer',
        'selection_land_size_unit' => 'integer',
        'selection_type_of_property_id' => 'integer',
        'selection_built_up_area_unit' => 'integer',
        'selection_land_category_id' => 'integer',
        'selection_land_status_id' => 'integer',
        'deleted_at' => 'datetime',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'deleted_by' => 'integer',
    ];

    /**
     * Get the collateral that owns this property.
     */
    public function collateral(): BelongsTo
    {
        return $this->belongsTo(Collateral::class);
    }

    /**
     * Get the land category selection.
     */
    public function landCategorySelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_land_category_id');
    }

    /**
     * Get the land status selection.
     */
    public function landStatusSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_land_status_id');
    }

    public function landSizeUnitSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_land_size_unit');
    }

    public function builtUpAreaOfPropertyUnitSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_built_up_area_unit');
    }

    public function propertyTypesSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_type_of_property_id');
    }

    public function landSizeSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_land_size_unit');
    }

    public function builtUpAreaSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_built_up_area_unit');
    }

    /**
     * Get the property owners for this collateral property.
     */
    public function propertyOwners(): HasMany
    {
        return $this->hasMany(CollateralPropertyOwner::class);
    }

    /**
     * Get the address for this collateral property.
     */
    public function address(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable');
    }
}
