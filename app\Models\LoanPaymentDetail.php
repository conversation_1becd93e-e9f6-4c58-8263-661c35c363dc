<?php

namespace App\Models;

use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoanPaymentDetail extends BaseModel
{
    use HasFactory, UniqueCodeTrait;

    protected $fillable = [
        'uuid',
        'code',
        'loan_payment_id',
        'loan_txn_id',
        'amount',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_payment_id' => 'integer',
            'loan_txn_id' => 'integer',
            'amount' => 'decimal:2',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the loan payment that owns this detail.
     */
    public function loanPayment(): BelongsTo
    {
        return $this->belongsTo(LoanPayment::class);
    }

    /**
     * Get the loan transaction that owns this detail.
     */
    public function loanTxn(): BelongsTo
    {
        return $this->belongsTo(LoanTxn::class);
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new loan payment detail.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix('LPD');
    }
}
