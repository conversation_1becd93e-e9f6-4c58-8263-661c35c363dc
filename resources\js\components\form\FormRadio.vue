<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import '@vueform/multiselect/themes/default.css';

interface Option {
    value: string | number;
    label: string;
}

interface Props {
    id: string;
    label: string;
    modelValue: number;
    required?: boolean;
    error?: string;
    class?: string;
    labelClass?: string;
    options: Option[];
}

const props = withDefaults(defineProps<Props>(), {
    required: false,
    labelClass: '',
});

const emit = defineEmits(['update:modelValue']);

const handleChange = (value: string | number | (string | number)[]) => {
    emit('update:modelValue', value);
};
</script>

<template>
    <Label :for="props.id" :class="props.labelClass">
        {{ props.label }}
        <RequiredIndicator v-if="props.required" />
    </Label>
    <RadioGroup :model-value="props.modelValue" @update:model-value="handleChange" :orientation="'horizontal'" class="flex space-x-4 py-3">
        <div v-for="option in props.options" class="flex items-center space-x-2">
            <RadioGroupItem :value="option.value" />
            <Label>{{ option.label }}</Label>
        </div>
    </RadioGroup>
    <InputError class="mt-1" :message="props.error" />
</template>
