<script setup lang="ts">
import { cn } from '@/lib/utils';
import { type HTMLAttributes } from 'vue';

interface LoadingOverlayProps {
    show?: boolean;
    class?: HTMLAttributes['class'];
}

const props = withDefaults(defineProps<LoadingOverlayProps>(), {
    show: false,
});
</script>

<template>
    <div
        v-if="props.show"
        data-slot="loading-overlay"
        :class="cn('fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm', props.class)"
    >
        <div class="flex flex-col items-center gap-2">
            <div class="border-primary size-12 animate-spin rounded-full border-4 border-t-transparent"></div>
        </div>
    </div>
</template>
