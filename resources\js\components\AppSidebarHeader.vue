<script setup lang="ts">
import AppSidebarTrigger from '@/components/AppSidebarTrigger.vue';
import { useServerTime } from '@/composables/useServerTime';
import type { BreadcrumbItemType } from '@/types';

withDefaults(
    defineProps<{
        breadcrumbs?: BreadcrumbItemType[];
    }>(),
    {
        breadcrumbs: () => [],
    },
);

const { currentTime, currentDate } = useServerTime();
</script>

<template>
    <header
        class="bg-card border-border-foreground/70 flex h-16 shrink-0 items-center border-b px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-14.5 md:px-4"
    >
        <!-- Left: Sidebar and Breadcrumbs -->
        <div class="flex items-center gap-2">
            <AppSidebarTrigger class="-ml-1" />
            <!-- <template v-if="breadcrumbs && breadcrumbs.length > 0">
                <Breadcrumbs :breadcrumbs="breadcrumbs" />
            </template> -->
        </div>

        <!-- Right: Server Time -->
        <div class="ml-auto flex min-w-[240px] items-center justify-end space-x-1 text-sm text-gray-600">
            <span class="text-foreground-600 hidden sm:inline">Server Time:</span>
            <span class="font-medium tracking-wider text-gray-800"> {{ currentDate }} {{ currentTime }} </span>
        </div>
    </header>
</template>
