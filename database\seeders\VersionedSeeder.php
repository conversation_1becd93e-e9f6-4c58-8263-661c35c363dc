<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

abstract class VersionedSeeder extends Seeder
{
    protected string $seederName;

    protected function applyVersionedSeeds(array $versionedData): void
    {
        $currentVersion = DB::table('seeder_versions')->where('seeder', $this->seederName)->value('version') ?? 0;

        foreach ($versionedData as $version => $data) {
            if ($version > $currentVersion) {
                $this->applyVersion($version, $data);

                DB::table('seeder_versions')
                    ->updateOrInsert(
                        ['seeder' => $this->seederName],
                        ['version' => $version, 'updated_at' => now()]
                    );

                $this->command->info("Applied {$this->seederName} version {$version}.");
            }
        }
    }

    abstract protected function applyVersion(int $version, array $data): void;

    abstract protected function getVersionedData(): array;
}
