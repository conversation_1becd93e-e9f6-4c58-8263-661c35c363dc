<?php

namespace Database\Factories;

use App\Enums\Collateral\CollateralStatus;
use App\Models\Collateral;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Collateral>
 */
class CollateralFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Collateral::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'selection_type_id' => null,
            'type' => fake()->word(),
            'name' => fake()->name(),
            'identity_no' => fake()->numerify('##########'),
            'status' => CollateralStatus::ACTIVE,
            'remark' => fake()->optional()->sentence(),
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Indicate that the collateral is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => CollateralStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the collateral is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => CollateralStatus::INACTIVE,
        ]);
    }
}
