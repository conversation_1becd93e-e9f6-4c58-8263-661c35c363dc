<?php

namespace App\Http\Resources\Users;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $adminProfile = $this->adminProfiles->first();
        $hierarchyData = $this->resolveHierarchy($adminProfile);

        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $adminProfile?->code,
            'username' => $this->username,
            'email' => $this->email,
            'status' => $this->status,
            'roles' => $this->roles->map(fn ($role) => [
                'id' => $role->id,
                'name' => $role->name,
                'is_headquarter' => $role->is_headquarter,
            ]),
            'headquarter' => $hierarchyData['headquarter'],
            'company' => $this->roles->contains(fn ($role) => $role->is_headquarter) ? '' : ($hierarchyData['company'] ?? ''),
            'team' => $hierarchyData['team'],
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];
    }

    private function resolveHierarchy($adminProfile): array
    {
        if (! $adminProfile) {
            return ['headquarter' => null, 'company' => null, 'team' => null];
        }

        if ($directHeadquarter = $adminProfile->headquarters->first()) {
            return [
                'headquarter' => $this->formatHeadquarter($directHeadquarter),
                'company' => null,
                'team' => null,
            ];
        }

        if ($directCompany = $adminProfile->companies->first()) {
            $headquarter = ($directCompany->headquarter_id && $directCompany->parent)
                ? $this->formatHeadquarter($directCompany->parent)
                : null;

            return [
                'headquarter' => $headquarter,
                'company' => $this->formatCompany($directCompany),
                'team' => null,
            ];
        }

        $directTeam = $adminProfile->teams->first();
        $company = null;
        $headquarter = null;

        if ($directTeam) {
            if ($teamCompany = $directTeam->companies->first()) {
                $company = $this->formatCompany($teamCompany);

                if ($teamCompany->headquarter_id && $teamCompany->parent) {
                    $headquarter = $this->formatHeadquarter($teamCompany->parent);
                }
            }
        }

        return [
            'headquarter' => $headquarter,
            'company' => $company,
            'team' => $directTeam ? $this->formatTeam($directTeam) : null,
        ];
    }

    private function formatHeadquarter($headquarter): array
    {
        return [
            'id' => $headquarter->id,
            'display_name' => $headquarter->display_name,
            'code' => $headquarter->code,
        ];
    }

    private function formatCompany($company): array
    {
        return [
            'id' => $company->id,
            'display_name' => $company->display_name,
            'code' => $company->code,
        ];
    }

    private function formatTeam($team): array
    {
        return [
            'id' => $team->id,
            'name' => $team->name,
        ];
    }
}
