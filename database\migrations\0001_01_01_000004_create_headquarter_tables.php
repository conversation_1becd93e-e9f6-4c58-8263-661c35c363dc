<?php

use App\Enums\Company\HeadquarterStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('headquarters', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('code', 36)->unique();

            // Headquarter information
            $table->string('name')->unique();
            $table->string('display_name');
            $table->unsignedTinyInteger('status')->default(HeadquarterStatus::ACTIVE->value);

            // Additional information
            $table->text('remark')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('headquarterables', function (Blueprint $table) {
            $table->foreignId('headquarter_id')->constrained('headquarters')->onDelete('cascade');
            $table->morphs('headquarterable');
            $table->primary(['headquarter_id', 'headquarterable_id', 'headquarterable_type'], 'headquarterables_primary');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('headquarterables');
        Schema::dropIfExists('headquarters');
    }
};
