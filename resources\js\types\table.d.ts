export interface Link {
    url: string | null;
    label: string;
    active: boolean;
}

export interface PaginationMeta {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
}

export interface PaginationProps {
    links: Link[];
}

export interface FilterOptions {
    per_page?: number;
    sort_field?: string;
    sort_direction?: 'asc' | 'desc';
    [key: string]: any; // Allow for additional custom filters
}

export interface PaginatedData<T> {
    data: T[];
    links: Link[];
    from: number;
    to: number;
    total: number;
    meta: PaginationMeta;
}
