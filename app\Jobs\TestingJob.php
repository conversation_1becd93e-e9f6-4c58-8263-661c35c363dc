<?php

namespace App\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable as BusQueueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class TestingJob implements ShouldQueue
{
    use BusQueueable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Check if the job is enabled.
     */
    public static function isEnabled(): bool
    {
        return (bool) env('TESTING_JOB_ENABLED', false);
    }

    /**
     * Get the cron frequency for the job.
     */
    public static function getFrequency(): string
    {
        return env('TESTING_JOB_FREQUENCY', '* * * * *');
    }

    /**
     * Create a new job instance.
     */
    public function __construct() {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('TestingJob executed: '.Carbon::now());
    }
}
