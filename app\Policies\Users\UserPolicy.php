<?php

namespace App\Policies\Users;

use App\Enums\AccessControl\PermissionName;
use App\Enums\AccessControl\RoleName;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any users.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_USERS->value);
    }

    /**
     * Determine whether the user can view the user.
     *
     * @return bool
     */
    public function view(User $user, User $model)
    {
        return $user->hasPermissionTo(PermissionName::READ_USERS->value);
    }

    /**
     * Determine whether the user can create users.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_USERS->value);
    }

    /**
     * Determine whether the user can update the user.
     *
     * @return bool
     */
    public function update(User $user, User $model)
    {
        // Prevent users from updating themselves unless they have the permission
        if ($user->id === $model->id && ! $user->hasPermissionTo(PermissionName::UPDATE_USERS->value)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_USERS->value);
    }

    /**
     * Determine whether the user can delete the user.
     *
     * @return bool
     */
    public function delete(User $user, User $model)
    {
        // Prevent users from deleting themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Prevent deleting super admin users unless the current user is also a super admin
        if ($model->hasRole(RoleName::SUPER_ADMIN->value) && ! $user->hasRole(RoleName::SUPER_ADMIN->value)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::DELETE_USERS->value);
    }

    /**
     * Determine whether the user can restore the user.
     *
     * @return bool
     */
    public function restore(User $user, User $model)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_USERS->value);
    }

    /**
     * Determine whether the user can permanently delete the user.
     *
     * @return bool
     */
    public function forceDelete(User $user, User $model)
    {
        // Only Super Administrator can force delete users
        return $user->hasRole(RoleName::SUPER_ADMIN->value);
    }
}
