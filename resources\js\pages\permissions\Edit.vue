<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Permission } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Props {
    permission: Permission;
}

const props = defineProps<Props>();

const form = useForm({
    name: props.permission.name,
});

const formFields = computed(() => [
    {
        id: 'name',
        label: 'Permission Name',
        type: 'input',
        required: true,
        placeholder: 'Permission Name',
        error: form.errors.name,
        modelValue: form.name,
        updateValue: (value: string) => (form.name = value),
    },
]);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('permissions.update', props.permission.id),
            entityName: 'permission',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Edit Permission" />
        <div class="px-4 py-3">
            <Heading title="Permission" pageNumber="P000050" description="Edit the selected permission record" />

            <AppCard title="Edit Permission" :form="form" backRoute="permissions.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
