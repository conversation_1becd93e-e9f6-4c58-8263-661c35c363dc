<?php

namespace App\Models;

use App\Enums\Customer\CustomerStatus;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * CustomerProfile model for managing customer information
 */
class CustomerProfile extends BaseModel
{
    use HasFactory, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'headquarter_id',
        'company_id',
        'code',
        'name',
        'display_name',
        'email',
        'identity_no',
        'old_identity_no',
        'registration_date',
        'years_of_incorporation',
        'age',
        'birth_date',
        'selection_type_id',
        'type',
        'status',
        'selection_gender_id',
        'gender',
        'selection_race_id',
        'race',
        'selection_nationality_id',
        'nationality',
        'selection_education_level_id',
        'education_level',
        'selection_marriage_status_id',
        'marriage_status',
        'remark',
        'status',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'age' => 'integer',
            'selection_type_id' => 'integer',
            'years_of_incorporation' => 'integer',
            'registration_date' => 'date',
            'birth_date' => 'date',
            'status' => CustomerStatus::class,
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Set the display name and automatically set the name to uppercase.
     *
     * @param  string  $value
     * @return void
     */
    public function setDisplayNameAttribute($value)
    {
        $this->attributes['display_name'] = $value;
        $this->attributes['name'] = strtoupper($value);
    }

    public function getBirthDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }

    public function getRegistrationDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }

    /**
     * Get the teams associated with this admin profile.
     */
    public function teams(): MorphToMany
    {
        return $this->morphToMany(Team::class, 'teamable');
    }

    public function customerContacts(): HasMany
    {
        return $this->hasMany(CustomerContact::class, 'customer_id');
    }

    public function customerAddresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class, 'customer_id');
    }

    public function employment(): HasOne
    {
        return $this->hasOne(CustomerEmployment::class, 'customer_id');
    }

    public function company(): HasOne
    {
        return $this->hasOne(CustomerCompany::class, 'customer_id');
    }

    public function customerCollaterals(): HasMany
    {
        return $this->hasMany(CustomerCollateral::class, 'customer_id');
    }

    public function customerDocuments(): HasMany
    {
        return $this->hasMany(CustomerDocument::class, 'customer_id');
    }

    public function loans(): HasMany
    {
        return $this->hasMany(Loan::class, 'customer_id');
    }

    /**
     * Get the selection type associated with this customer profile.
     */
    public function selectionType(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_type_id');
    }

    /**
     * Get the selection gender associated with this customer profile.
     */
    public function selectionGender(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_gender_id');
    }

    /**
     * Get the selection race associated with this customer profile.
     */
    public function selectionRace(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_race_id');
    }

    /**
     * Get the selection nationality associated with this customer profile.
     */
    public function selectionNationality(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_nationality_id');
    }

    /**
     * Get the selection education level associated with this customer profile.
     */
    public function selectionEducationLevel(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_education_level_id');
    }

    /**
     * Get the selection marriage status associated with this customer profile.
     */
    public function selectionMarriageStatus(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_marriage_status_id');
    }

    /**
     * Get customer profile accessible to the specified user.
     */
    public static function forUser(?User $user = null): Builder
    {
        $user = $user ?? auth()->user();

        if ($user->isSuperAdmin()) {
            return self::query();
        }

        return self::whereIn('company_id', $user->getAccessibleCompanyIds());
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode($model->headquarter_id);
            }
        });
    }

    /**
     * Generate a unique code for a new headquarter.
     */
    public static function generateUniqueCode(int $headquarter_id): string
    {
        return self::generateUniqueCodeWithPrefix(uniqueFields: ['headquarter_id' => $headquarter_id], padLength: 5);
    }
}
