<script setup lang="ts">
import { Tabs, Tabs<PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'; // adjust if needed
import { defineModel, defineProps } from 'vue';

const modelValue = defineModel<string>();
const props = defineProps<{
    tabs: { label: string; value: string; disabled?: boolean }[];
    title?: string;
}>();
</script>

<template>
    <Tabs v-model="modelValue" class="w-full gap-0">
        <TabsList
            :class="[
                'grid w-full rounded-none',
                {
                    'grid-cols-2': tabs.length === 2,
                    'grid-cols-3': tabs.length === 3,
                    'grid-cols-4': tabs.length === 4,
                    'grid-cols-5': tabs.length === 5,
                    'grid-cols-6': tabs.length === 6,
                    'grid-cols-7': tabs.length === 7,
                },
            ]"
        >
            <TabsTrigger
                v-for="tab in tabs"
                :key="tab.value"
                :value="tab.value"
                :disabled="tab.disabled"
                class="flex w-full data-[state=active]:bg-white data-[state=active]:text-blue-600"
            >
                <span>{{ tab.label }}</span>
            </TabsTrigger>
        </TabsList>

        <TabsContent v-for="tab in tabs" :key="tab.value" :value="tab.value">
            <slot :name="tab.value" />
        </TabsContent>
    </Tabs>
</template>
