<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

interface Props {
    customer: any;
    index?: number;
    label?: string;
    removeCoBorrowers?: (index: number) => void;
    ordinal?: (n: number) => string;
}

interface Address {
    id: number;
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    state: string | null;
    selection_state_id: number | null;
    state_selection: string | null;
    country: string | null;
    selection_country_id: number | null;
    country_selection: string | null;
}

const formatAddress = (address: Address | null) => {
    if (!address) return 'No address provided';

    const line1 = address.line_1;
    const line2 = address.line_2;
    const line3Parts = [address.postcode, address.city, address.state_selection || address.state, address.country_selection || address.country]
        .filter(Boolean)
        .join(', ');

    return [line1, line2, line3Parts].filter(Boolean).join('\n');
};

function formatDate(dateString: string | null) {
    if (!dateString) return '-';
    return new Date(dateString).toISOString().slice(0, 10); // Formats as 'YYYY-MM-DD'
}
const props = defineProps<Props>();
</script>
<template>
    <Accordion type="single" class="w-full" collapsible>
        <AccordionItem value="item-1" class="my-2">
            <Card class="gap-0 rounded-xs py-0">
                <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                    <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                        <FaIcon name="plus" />
                    </span>

                    <!-- Minus icon: visible when open -->
                    <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                        <FaIcon name="minus" />
                    </span>

                    <span class="flex-1 text-left font-medium">{{ props.label ?? 'Main Borrower' }}</span>

                    <template v-if="props.index !== undefined" #icon>
                        <Button
                            type="button"
                            @click.stop="props.removeCoBorrowers?.(props.index)"
                            variant="destructive"
                            class="flex items-center gap-1"
                        >
                            <FaIcon name="trash" />
                            Delete
                        </Button>
                    </template>
                </AccordionTrigger>
                <Separator />
                <AccordionContent class="p-2">
                    <Label class="text-[20px]">Personal Details</Label>
                    <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                        <template v-if="props.customer.customer_type === 'Personal'">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between px-1">
                                    <Label for="customer-type:">Cutomer Type </Label>
                                    <p>{{ props.customer.customer_type }}</p>
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="name">Name </Label>
                                    {{ props.customer.name }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="identity_no">Identity No </Label>
                                    {{ props.customer.identity_no }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="dob">Date of Birth </Label>
                                    {{ formatDate(props.customer.birth_date) }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="age">Age </Label>
                                    {{ props.customer.age }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="race">Race </Label>
                                    {{ props.customer.race ?? props.customer.race_selection }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="gender">Gender </Label>
                                    {{ props.customer.gender ?? props.customer.gender_selection }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="marital_status">Marital Status </Label>
                                    {{ props.customer.marriage_status ?? props.customer.marriage_status_selection }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="nationality">Nationality </Label>
                                    {{ props.customer.nationality ?? props.customer.nationality_selection }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="education_level">Education Level </Label>
                                    {{ props.customer.education_level ?? props.customer.education_level_selection }}
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between px-1">
                                    <Label for="customer-type:">Cutomer Type</Label>
                                    <p>{{ props.customer.customer_type }}</p>
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="name">Name </Label>
                                    {{ props.customer.name }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="identity_no">New Business Registration No. </Label>
                                    {{ props.customer.identity_no }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="old_business_registration_no">Old Business Registration No. </Label>
                                    {{ props.customer.old_identity_no }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="date_of_registration">Date of Registration </Label>
                                    {{ formatDate(props.customer.registration_date) }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="years_of_incorporation">Years of Incorporation </Label>
                                    {{ props.customer.years_of_incorporation }}
                                </div>
                            </div>
                        </template>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between px-1">
                                <Label for="email">Email </Label>
                                {{ props.customer.email ?? '-' }}
                            </div>
                            <div class="flex items-start justify-between px-1">
                                <Label for="years_of_incorporation"> Contacts </Label>
                                <div class="flex flex-col text-right">
                                    <div v-for="(contact, index) in props.customer.contacts" :key="index">
                                        ({{ contact.contact_country_selection }}) {{ contact.contact }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <Separator class="my-4" />
                    <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                        <div v-for="(address, index) in props.customer.addresses" :key="index" class="space-y-1">
                            <Label :for="`address-${index}`" class="font-semibold"> Address {{ index + 1 }} </Label>
                            <p class="whitespace-pre-wrap">
                                {{ formatAddress(address) }}
                            </p>
                        </div>
                    </div>
                    <Separator class="my-4" />
                    <div class="pb-3">
                        <Label for="remark" class="text-base">Remark </Label>
                        {{ props.customer.remark ?? '- ' }}
                    </div>
                    <template v-if="props.customer.customer_type === 'Personal'">
                        <Label class="text-[20px]">Employment Info</Label>
                        <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between px-1">
                                    <Label for="">Terms of Employment</Label>
                                    <p>
                                        {{ props.customer.employment.terms_of_employment ?? props.customer.employment.terms_of_employment_selection }}
                                    </p>
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="">Employment Name</Label>
                                    <p>{{ props.customer.employment.employer_name }}</p>
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="">Job Position</Label>
                                    <p>{{ props.customer.employment.job_position }}</p>
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="">Occupation</Label>
                                    <p>{{ props.customer.employment.occupation_selection }}</p>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between px-1">
                                    <Label for="">Length of Service (Year)</Label>
                                    <p>{{ props.customer.employment.length_service_month }}</p>
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="">Length of Service (Year)</Label>
                                    <p>{{ props.customer.employment.length_service_year }}</p>
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="">Business Classification</Label>
                                    <p>{{ props.customer.employment.business_category_selection }}</p>
                                </div>
                            </div>
                        </div>
                        <Separator class="my-4" />
                        <dl class="grid grid-cols-2 gap-x-8">
                            <div class="grid grid-cols-3 py-3">
                                <Label class="font-medium">Gross Income (Monthly)</Label>
                                <dd class="col-span-2 text-right text-sm">{{ props.customer.employment.gross_income }}</dd>
                            </div>
                            <div class="grid grid-cols-3 py-3">
                                <Label class="font-medium">Net Income (Monthly)</Label>
                                <dd class="col-span-2 text-right text-sm">{{ props.customer.employment.net_income }}</dd>
                            </div>
                        </dl>
                        <Separator class="my-4" />
                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between px-1">
                                    <Label for="" class="pb-2">Telephone</Label>
                                    {{ props.customer.employment.telephone_country_selection }} {{ props.customer.employment.telephone }}
                                </div>
                                <div class="flex items-center justify-between px-1">
                                    <Label for="" class="pb-2">Mobile Phone</Label>
                                    {{ props.customer.employment.mobile_country_selection }} {{ props.customer.employment.mobile_phone }}
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <Label for="" class="pb-2">Address</Label>
                                    <p class="whitespace-pre-wrap">{{ formatAddress(props.customer.employment.address) || '-' }}</p>
                                </div>
                            </div>
                        </div>
                    </template>
                    <template v-if="props.customer.customer_type === 'Business'">
                        <Label class="text-[20px]">Company Details</Label>
                        <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                            <div class="flex items-center justify-between px-1">
                                <Label for="name" class="text-base">Nature of Business </Label>
                                {{ props.customer.company.nature_of_business_selection ?? null }}
                            </div>
                            <div class="flex items-center justify-between px-1">
                                <Label for="name" class="text-base">Country of Business Operation </Label>
                                {{ props.customer.company.country_of_business_selection ?? null }}
                            </div>
                            <div class="flex items-center justify-between px-1">
                                <Label for="current_paid_up_capital" class="text-base">Current Paid Up Capital (RM) </Label>
                                {{ props.customer.company.current_paid_up_capital ?? null }}
                            </div>
                            <div class="flex items-center justify-between px-1">
                                <Label for="business_turnover" class="text-base">Business Turnover (RM) </Label>
                                {{ props.customer.company.business_turnover ?? null }}
                                {{ formatDate(props.customer.company.business_turnover_date) ?? null }}
                            </div>
                            <div class="flex items-center justify-between px-1">
                                <Label for="business_net_income" class="text-base">Business Net Income (RM) </Label>
                                {{ props.customer.company.business_net_income ?? null }}
                                {{ formatDate(props.customer.company.business_net_income_date) ?? null }}
                            </div>
                        </div>
                        <Separator class="my-4" />
                        <Accordion type="multiple" class="w-full" collapsible>
                            <AccordionItem v-for="(owner, index) in props.customer.owners" :key="index" :value="String(index)" class="mb-1">
                                <Card class="gap-0 rounded-xs py-0">
                                    <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                        <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                            <FaIcon name="plus" />
                                        </span>
                                        <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                            <FaIcon name="minus" />
                                        </span>
                                        <span class="flex-1 py-2 text-left font-medium">{{ ordinal(index + 1) }} Owner </span>
                                    </AccordionTrigger>
                                    <Separator />
                                    <AccordionContent class="p-2">
                                        <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                            <div class="flex items-center justify-between px-1">
                                                <Label class="text-base"> Owner Type </Label>
                                                {{ owner.onwer_type }}
                                            </div>
                                            <div></div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label class="text-base"> Name </Label>
                                                {{ owner.name }}
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label class="text-base"> Identity No </Label>
                                                {{ owner.identity_no }}
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label class="text-base"> Nationality </Label>
                                                {{ owner.nationality_selection ?? owner.nationality }}
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label class="text-base"> Share (%) </Label>
                                                {{ owner.share_unit }}
                                            </div>
                                        </div>
                                    </AccordionContent>
                                </Card>
                            </AccordionItem>
                        </Accordion>
                    </template>
                </AccordionContent>
            </Card>
        </AccordionItem>
    </Accordion>
</template>
