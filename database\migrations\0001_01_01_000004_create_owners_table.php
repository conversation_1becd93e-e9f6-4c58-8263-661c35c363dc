<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('owners', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Polymorphic relation
            $table->morphs('ownerable');

            // Owner information
            $table->string('name')->nullable();
            $table->string('identity_no', 20)->nullable();

            // Selection references
            $table->foreignId('selection_type_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('type', 36)->nullable();
            $table->foreignId('selection_nationality_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('nationality', 72)->nullable();
            $table->decimal('share_unit', 3, 2)->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('owners');
    }
};
