{"containerDefinitions": [{"name": "${CONTAINER_NAME}", "image": "${CONTAINER_IMAGE_REGISTRY_URL}/${IMAGE_NAME}:${VERSION}", "cpu": 0, "portMappings": [{"name": "${CONTAINER_NAME}", "containerPort": 80, "hostPort": 80, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [], "mountPoints": [{"sourceVolume": "laravel-log", "containerPath": "/var/www/storage/logs"}, {"sourceVolume": "supervisor-log", "containerPath": "/var/log/supervisor"}, {"sourceVolume": "php-log", "containerPath": "/opt/bitnami/php/logs"}, {"sourceVolume": "nginx-log", "containerPath": "/var/log/nginx"}], "volumesFrom": [], "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost/up || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 10}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "${AWS_LOG_GROUP_NAME}", "awslogs-region": "${AWS_REGION}", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}}], "family": "${TASK_DEFINITION_FAMILY_NAME}", "executionRoleArn": "arn:aws:iam::${AWS_ACCOUNT_ID}:role/ecsTaskExecutionRole", "networkMode": "bridge", "volumes": [{"name": "laravel-log", "host": {"sourcePath": "/var/log/laravel"}}, {"name": "supervisor-log", "host": {"sourcePath": "/var/log/supervisor"}}, {"name": "php-log", "host": {"sourcePath": "/var/log/php"}}, {"name": "nginx-log", "host": {"sourcePath": "/var/log/nginx"}}], "placementConstraints": [], "requiresCompatibilities": ["EC2"], "cpu": "256", "memory": "256", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}}