<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

trait HasStatus
{
    /**
     * Scope to filter records by status.
     */
    public function scopeWithStatus(Builder $query, Request $request, string $columnField = 'status', string $requestField = 'status'): Builder
    {
        return $query->when($request->filled($requestField), function ($query) use ($request, $columnField, $requestField) {
            $query->where($columnField, $request->input($requestField));
        });
    }
}
