import Swal from 'sweetalert2';

export function useSweetAlert() {
    const confirm = async (options: { title: string; html?: string; text?: string; confirmText?: string; cancelText?: string }) => {
        const result = await Swal.fire({
            title: options.title,
            html: options.html || '',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: options.confirmText || 'Yes',
            cancelButtonText: options.cancelText || 'Cancel',
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded',
                cancelButton: 'bg-red-600 hover:bg-red-400 text-white px-4 py-2 rounded ml-2',
            },
        });
        return result.isConfirmed;
    };

    const success = (text: string, method?: string) => {
        const { past } = getActionText(method);
        Swal.fire({
            icon: 'success',
            title: 'Successfully ' + past,
            text,
            confirmButtonText: 'OK',
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded',
            },
        });
    };

    const error = (text: string, method?: string) => {
        const { present } = getActionText(method);
        Swal.fire({
            icon: 'error',
            title: `${present} Failed`,
            text,
            confirmButtonText: 'OK',
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-red-600 hover:bg-red-500 text-white px-4 py-2 rounded',
            },
        });
    };

    const getActionText = (method?: string) => {
        if (!method) return { present: 'Update', past: 'Updated' };

        switch (method) {
            case 'post':
                return { present: 'Creation', past: 'Created' };
            case 'put':
            case 'patch':
                return { present: 'Update', past: 'Updated' };
            case 'delete':
                return { present: 'Deletion', past: 'Deleted' };
            default:
                return { present: 'Update', past: 'Updated' };
        }
    };

    return { confirm, success, error };
}
