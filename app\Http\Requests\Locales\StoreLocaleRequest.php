<?php

namespace App\Http\Requests\Locales;

use App\Http\Requests\BaseRequest;

class StoreLocaleRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'word' => 'required|string|max:100|unique:locales,word',
            'en' => 'nullable|string',
            'zh' => 'nullable|string',
            'status' => 'required|integer',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'word.required' => 'The locale word is required.',
            'word.unique' => 'This locale word already exists.',
        ];
    }
}
