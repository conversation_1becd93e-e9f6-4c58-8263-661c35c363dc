<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('collateral_valuers', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Relationships
            $table->foreignId('collateral_id')->constrained('collaterals')->cascadeOnDelete();

            // Valuation details
            $table->decimal('valuation_amount', 24, 2)->nullable();
            $table->string('valuer', 30)->nullable();
            $table->datetime('valuation_received_date')->nullable();
            $table->datetime('land_search_received_date')->nullable();
            $table->boolean('is_primary')->default(false);

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('collateral_valuers');
    }
};
