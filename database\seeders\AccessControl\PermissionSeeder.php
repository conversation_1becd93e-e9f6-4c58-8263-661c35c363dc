<?php

namespace Database\Seeders\AccessControl;

use App\Enums\AccessControl\PermissionName;
use App\Enums\AccessControl\RoleName;
use Database\Seeders\VersionedSeeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends VersionedSeeder
{
    protected string $seederName = 'permissions';

    public function run(): void
    {
        $this->applyVersionedSeeds($this->getVersionedData());

        $this->assignPermissionsToRoles();
    }

    protected function applyVersion(int $version, array $permissionGroups): void
    {
        foreach ($permissionGroups as $group => $permissions) {
            foreach ($permissions as $permission) {
                Permission::firstOrCreate(['name' => $permission]);

                $this->command->line("Created permission: {$permission}");
            }
        }
    }

    protected function getVersionedData(): array
    {
        return [
            1 => PermissionName::groupedByResource(),
        ];
    }

    private function assignPermissionsToRoles(): void
    {
        foreach (RoleName::cases() as $roleEnum) {
            $roleName = $roleEnum->value;

            $role = Role::where('name', $roleName)->first();

            if (! $role) {
                $this->command->warn("Role '{$roleName}' not found. Skipping permission assignment.");

                continue;
            }

            $permissions = PermissionName::forRole($roleName);

            $role->syncPermissions($permissions);

            $this->command->info("Assigned permissions to {$roleName} role.");
        }
    }
}
