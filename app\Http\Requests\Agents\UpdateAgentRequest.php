<?php

namespace App\Http\Requests\Agents;

use App\Enums\Agent\AgentStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateAgentRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => [
                'nullable',
                'email',
                'max:255',
            ],
            'status' => ['required', new Enum(AgentStatus::class)],
            'remark' => ['nullable', 'string', 'max:1000'],
        ];
    }
}
