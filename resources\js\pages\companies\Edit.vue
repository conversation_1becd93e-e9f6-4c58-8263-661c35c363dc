<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FileUpload from '@/components/form/file_upload/FileUpload.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Company, Headquarter } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const { submitWithConfirmation } = useFormSubmit();
const { formatEnumOptions } = useFormatOptions();

interface Props {
    company: Company;
    headquarters: Headquarter[];
    logo: any;
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    display_name: props.company.display_name,
    business_registration_no: props.company.business_registration_no,
    old_business_registration_no: props.company.old_business_registration_no,
    status: Number(props.company.status),
    headquarter_id: props.company.headquarter_id,
    logo: props.logo ?? null,
});

const formFields = computed(() => [
    {
        id: 'display_name',
        label: 'Company Name',
        type: 'show',
        required: false,
        modelValue: form.display_name,
        updateValue: (value: string) => (form.display_name = value),
    },
    {
        id: 'headquarter_id',
        label: 'Headquarter Name',
        type: 'show',
        required: false,
        modelValue: props.headquarters.find((h) => h.id === form.headquarter_id)?.display_name || '',
    },
    {
        id: 'business_registration_no',
        label: 'New Business Registration No.',
        type: 'input',
        required: false,
        placeholder: 'New Business Registration No.',
        error: form.errors.business_registration_no,
        modelValue: form.business_registration_no,
        updateValue: (value: string) => (form.business_registration_no = value),
    },
    {
        id: 'old_business_registration_no',
        label: 'Old Business Registration No.',
        type: 'input',
        required: false,
        placeholder: 'Old Business Registration No.',
        error: form.errors.old_business_registration_no,
        modelValue: form.old_business_registration_no,
        updateValue: (value: string) => (form.old_business_registration_no = value),
    },
    {
        id: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        placeholder: 'Status',
        error: form.errors.status,
        options: formatEnumOptions(props.statuses),
        modelValue: form.status,
        updateValue: (value: number) => (form.status = value),
    },
]);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('companies.update', props.company.id),
            entityName: 'company',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Edit Company" />
        <div class="px-4 py-3">
            <Heading title="Company" pageNumber="P000007" description="Edit the selected company record" />

            <AppCard title="Edit Company" :form="form" backRoute="companies.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                                :class="field.class"
                            />
                            <FileUpload id="logo" label="Logo" v-model="form.logo" isEdit />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
