<?php

namespace App\Policies\Selections;

use App\Enums\AccessControl\PermissionName;
use App\Models\Selection;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SelectionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_SELECTIONS->value);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function view(User $user, Selection $selection)
    {
        return $user->hasPermissionTo(PermissionName::READ_SELECTIONS->value);
    }

    /**
     * Determine whether the user can create models.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_SELECTIONS->value);
    }

    /**
     * Determine whether the user can update the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function update(User $user, Selection $selection)
    {
        return $user->hasPermissionTo(PermissionName::UPDATE_SELECTIONS->value);
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function delete(User $user, Selection $selection)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_SELECTIONS->value);
    }

    /**
     * Determine whether the user can restore the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function restore(User $user, Selection $selection)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_SELECTIONS->value);
    }

    /**
     * Determine whether the user can permanently delete the model.
     *
     * @return \Illuminate\Auth\Access\Response|bool
     */
    public function forceDelete(User $user, Selection $selection)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_SELECTIONS->value);
    }
}
