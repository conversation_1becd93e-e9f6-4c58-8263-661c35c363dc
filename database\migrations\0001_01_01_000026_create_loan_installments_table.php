<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loan_installments', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('code', 36)->unique();

            // Foreign key relationships
            $table->foreignId('loan_id')->constrained('loans')->cascadeOnDelete();

            // Installment information
            $table->datetime('pay_date')->comment('Installment pay date');
            $table->unsignedInteger('tenure')->comment('Installment number (e.g., month 1 of 12)');
            $table->decimal('total_amount', 24, 2)->comment('Total due for this installment');
            $table->decimal('principle_amount', 24, 2)->comment('Principal portion of the installment');
            $table->decimal('interest_amount_net', 24, 2)->comment('Interest amount after applying rebates (net interest)');
            $table->decimal('interest_amount_gross', 24, 2)->comment('Original interest amount before any rebates (gross interest)');
            $table->decimal('outstanding_balance_amount', 24, 2)->comment('Outstanding balance after this installment');
            $table->datetime('due_date')->comment('Installment due date');
            $table->unsignedTinyInteger('status');

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['loan_id', 'tenure']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loan_installments');
    }
};
