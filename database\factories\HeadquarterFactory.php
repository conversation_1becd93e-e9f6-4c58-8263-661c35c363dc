<?php

namespace Database\Factories;

use App\Enums\Company\HeadquarterStatus;
use App\Models\Headquarter;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Headquarter>
 */
class HeadquarterFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Headquarter::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $displayName = fake()->company();

        return [
            'uuid' => Str::uuid(),
            'code' => 'HQ'.strtoupper(Str::random(6)),
            'display_name' => $displayName,
            'name' => strtoupper($displayName),
            'status' => HeadquarterStatus::ACTIVE,
            'remark' => fake()->optional(0.7)->sentence(),
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Indicate that the headquarter is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => HeadquarterStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the headquarter is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => HeadquarterStatus::INACTIVE,
        ]);
    }
}
