<?php

namespace App\Policies\Teams;

use App\Enums\AccessControl\PermissionName;
use App\Models\Team;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class TeamPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any teams.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_TEAMS->value);
    }

    /**
     * Determine whether the user can view the team.
     *
     * @return bool
     */
    public function view(User $user, Team $team)
    {
        return $user->hasPermissionTo(PermissionName::READ_TEAMS->value);
    }

    /**
     * Determine whether the user can create teams.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_TEAMS->value);
    }

    /**
     * Determine whether the user can update the team.
     *
     * @return bool
     */
    public function update(User $user, Team $team)
    {
        if (! $user->hasTeamAccess($team->id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_TEAMS->value);
    }

    /**
     * Determine whether the user can delete the team.
     *
     * @return bool
     */
    public function delete(User $user, Team $team)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_TEAMS->value);
    }

    /**
     * Determine whether the user can restore the team.
     *
     * @return bool
     */
    public function restore(User $user, Team $team)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_TEAMS->value);
    }

    /**
     * Determine whether the user can permanently delete the team.
     *
     * @return bool
     */
    public function forceDelete(User $user, Team $team)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_TEAMS->value);
    }
}
