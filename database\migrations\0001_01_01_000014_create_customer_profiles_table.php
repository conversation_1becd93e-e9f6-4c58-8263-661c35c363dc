<?php

use App\Enums\Customer\CustomerStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('customer_profiles', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('headquarter_id')->constrained('headquarters')->onDelete('cascade');
            $table->foreignId('company_id')->constrained('companies')->onDelete('cascade');
            $table->string('code', 36);

            // Personal information
            $table->string('name')->nullable();
            $table->string('display_name')->nullable();
            $table->string('email')->nullable();
            $table->string('identity_no', 20)->nullable();
            $table->string('old_identity_no', 20)->nullable();
            $table->string('registration_date')->nullable();
            $table->integer('years_of_incorporation')->nullable();
            $table->integer('age')->nullable();
            $table->string('birth_date')->nullable();

            // Selection references
            $table->foreignId('selection_type_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('type', 36)->nullable();
            $table->foreignId('selection_gender_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('gender', 36)->nullable();
            $table->foreignId('selection_race_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('race', 36)->nullable();
            $table->foreignId('selection_nationality_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('nationality', 72)->nullable();
            $table->foreignId('selection_education_level_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('education_level')->nullable();
            $table->foreignId('selection_marriage_status_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('marriage_status', 36)->nullable();
            $table->unsignedTinyInteger('status')->default(CustomerStatus::ACTIVE->value);

            // Additional information
            $table->text('remark')->nullable();

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['headquarter_id', 'code']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('customer_profiles');
    }
};
