<script setup lang="ts">
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import TeamSwitcher from '@/components/TeamSwitcher.vue';
import type { SidebarProps } from '@/components/ui/sidebar';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarRail } from '@/components/ui/sidebar';
import { useSidebarNavigation } from '@/composables/useSidebarNavigation';
import { Separator } from './ui/separator';

const props = withDefaults(defineProps<SidebarProps>(), {
    collapsible: 'icon',
});

const { userData, navItems } = useSidebarNavigation();
</script>

<template>
    <Sidebar v-bind="props">
        <SidebarHeader class="gap-4 group-data-[state=expanded]:gap-0">
            <TeamSwitcher :teams="userData.teams" />
            <Separator class="bg-sidebar-border/80" />
        </SidebarHeader>
        <SidebarContent class="overflow-auto [scrollbar-width:none]">
            <NavMain :items="navItems" />
        </SidebarContent>
        <SidebarFooter>
            <Separator class="bg-sidebar-border/80" />
            <NavUser :user="userData.user" />
        </SidebarFooter>
        <SidebarRail />
    </Sidebar>
</template>
