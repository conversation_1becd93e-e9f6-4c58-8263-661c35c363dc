<?php

namespace Database\Seeders\Company;

use App\Models\Headquarter;
use Illuminate\Database\Seeder;

class HeadquarterSeeder extends Seeder
{
    public function run(): void
    {
        $headquarters = [
            ['name' => 'HEADQUARTER001', 'display_name' => 'Headquarter001'],
            ['name' => 'HEADQUARTER002', 'display_name' => 'Headquarter002'],
            ['name' => 'HEADQUARTER003', 'display_name' => 'Headquarter003'],
            ['name' => 'HEADQUARTER004', 'display_name' => 'Headquarter004'],
            ['name' => 'HEADQUARTER005', 'display_name' => 'Headquarter005'],
        ];

        foreach ($headquarters as $hq) {
            $headquarter = Headquarter::firstOrCreate(
                ['name' => $hq['name']],
                [
                    'display_name' => $hq['display_name'],
                ]
            );

            $headquarter->companies()->firstOrCreate(
                ['name' => $hq['name']],
                [
                    'display_name' => $hq['display_name'],
                    'is_headquarter' => true,
                ]
            );
        }

        $this->command->info('Headquarter created successfully');
    }
}
