<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Address model for managing address information
 */
class Address extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'uuid',
        'addressable_type',
        'addressable_id',
        'category',
        'selection_type_id',
        'type',
        'company_name',
        'line_1',
        'line_2',
        'postcode',
        'city',
        'selection_state_id',
        'state',
        'selection_country_id',
        'country',
        'is_primary',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'is_primary' => 'boolean',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the parent addressable model.
     */
    public function address(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the state selection.
     */
    public function stateSelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_state_id');
    }

    /**
     * Get the country selection.
     */
    public function countrySelection(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_country_id');
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->category)) {
                $model->category = 0;
            }

            if (empty($model->is_primary)) {
                $model->is_primary = false;
            }
        });
    }
}
