<script setup lang="ts">
import AdditionalPropertyOwner from '@/components/collateral/AdditionalPropertyOwner.vue';
import Valuation from '@/components/collateral/Valuation.vue';
import FaIcon from '@/components/FaIcon.vue';
import FormSelect from '@/components/form/FormSelect.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { formatDate } from '@/lib/utils';
import { computed, ref } from 'vue';

interface Selection {
    id: number | null;
    value: string | null;
}

interface Address {
    id: number;
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    state: string | null;
    selection_state_id: number | null;
    state_selection: string | null;
    country: string | null;
    selection_country_id: number | null;
    country_selection: string | null;
}

const openAccordion = ref('0');
const openValuersAccordion = ref('0');
const openOwnersAccordion = ref('0');
const props = defineProps<{
    form: any;
    errors?: Record<string, string[] | string>;
    addCollateral?: () => void;
    removeCollateral?: (index: number) => void;
    customerTypes?: Selection[];
    collateralTypes?: Selection[];
    propertyTypes?: Selection[];
    squareTypes?: Selection[];
    landCategories?: Selection[];
    landStatuses?: Selection[];
    states?: Selection[];
    countries?: Selection[];
    mobileCountries?: Selection[];
    telephoneCountries?: Selection[];
    ordinal: (n: number) => string;
    isEdit?: boolean;
    isShow?: boolean;
    isApplyLoan?: boolean;
    isFooter?: boolean;
    isAccordion?: boolean;
    goBack?: () => void;
    goNext?: () => void;
}>();

const selectedCollateralType = computed(() => props.collateralTypes?.find((type) => type.id === props.form.selection_type_id));

const isOtherCollateral = (collateral: any) => {
    const type = props.collateralTypes?.find((t) => t.id === collateral.selection_type_id);
    return type?.value === 'Other';
};

const formatAddress = (address: Address | null) => {
    if (!address) return 'No address provided';

    const line1 = address.line_1;
    const line2 = address.line_2;
    const line3Parts = [address.postcode, address.city, address.state_selection || address.state, address.country_selection || address.country]
        .filter(Boolean)
        .join(', ');

    return [line1, line2, line3Parts].filter(Boolean).join('\n');
};
</script>

<template>
    <CardContent class="px-0 py-0">
        <div v-if="!props.isApplyLoan" class="flex justify-between pb-3">
            <Label class="text-[20px]">Collateral Info</Label>
            <Button v-if="!isShow" type="button" class="bg-teal hover:bg-teal-hover flex items-center gap-2" @click="addCollateral">
                <FaIcon name="plus" />
                Add Collateral
            </Button>
        </div>
        <Accordion v-if="isAccordion" type="single" class="w-full" collapsible v-model="openAccordion">
            <AccordionItem v-for="(collateral, index) in props.form" :key="index" :value="String(index)" class="mb-1">
                <Card v-if="!collateral._delete" class="gap-0 rounded-xs py-0">
                    <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                        <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                            <FaIcon name="plus" />
                        </span>
                        <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                            <FaIcon name="minus" />
                        </span>
                        <span class="flex-1 py-2 text-left font-medium">{{ ordinal(index + 1) }} Collateral </span>
                        <template v-if="!isShow || isApplyLoan" #icon>
                            <Button type="button" @click.stop="removeCollateral(index)" variant="destructive" class="flex items-center gap-1">
                                <FaIcon name="trash" />
                                Delete
                            </Button>
                        </template>
                    </AccordionTrigger>
                    <Separator />
                    <AccordionContent class="p-2">
                        <div :class="[isShow ? 'grid grid-cols-2 gap-3 lg:grid-cols-2' : 'grid grid-cols-2 gap-4 lg:grid-cols-2']">
                            <!-- Collateral Type -->
                            <div :class="[isShow ? 'flex items-center justify-between px-1' : 'col-span-1']">
                                <Label :for="`selection_type_id.${index}`" class="text-base">
                                    Collateral Type
                                    <RequiredIndicator v-if="!isShow" />
                                </Label>
                                <p v-if="(isEdit && collateral.id) || isShow">{{ collateral.typeSelection }}</p>
                                <Select v-else v-model="collateral.selection_type_id" :error="errors[`${index}.selection_type_id`]">
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Select collateral type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem v-for="type in collateralTypes" :key="type.id" :value="type.id">
                                            {{ type.value }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                                <div v-if="errors?.[`${index}.selection_type_id`]" class="mt-1 text-sm text-red-500">
                                    {{ errors?.[`${index}.selection_type_id`] }}
                                </div>
                            </div>
                            <div></div>
                            <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                <Label :for="`name.${index}`" class="text-base">
                                    Owner Name
                                    <RequiredIndicator v-if="!isShow" />
                                </Label>
                                <p v-if="(isEdit && collateral.id) || isShow">{{ collateral.name }}</p>
                                <Input
                                    v-else
                                    :id="`name-${index}`"
                                    v-model="collateral.name"
                                    :error="props.errors?.[`collateral.${index}.name`]"
                                    required
                                    placeholder="Owner Name"
                                />
                                <div v-if="props.errors?.[`collateral.${index}.name`]" class="mt-1 text-sm text-red-500">
                                    {{ props.errors?.[`collateral.${index}.name`] }}
                                </div>
                            </div>

                            <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                <Label :for="`identity_no.${index}`" class="text-base">
                                    Owner Identify No
                                    <RequiredIndicator v-if="!isShow" />
                                </Label>
                                <p v-if="(isEdit && collateral.id) || isShow">{{ collateral.identity_no }}</p>
                                <Input
                                    v-else
                                    :id="`identity_no.${index}`"
                                    v-model="collateral.identity_no"
                                    :error="collateral.errors?.identity_no"
                                    required
                                    placeholder="Owner Identify No"
                                />
                                <div v-if="props.errors?.[`collateral.${index}.identity_no`]" class="mt-1 text-sm text-red-500">
                                    {{ props.errors?.[`collateral.${index}.identity_no`] }}
                                </div>
                            </div>

                            <div v-if="isOtherCollateral(collateral)" :class="[isShow ? 'flex items-center justify-between px-1' : 'col-span-2']">
                                <Label :for="`remark.${index}`" class="text-base">
                                    Remark
                                    <RequiredIndicator v-if="!isShow" />
                                </Label>
                                <p v-if="isShow">{{ collateral.remark }}</p>
                                <Textarea
                                    v-else
                                    :id="`remark.${index}`"
                                    v-model="collateral.remark"
                                    :error="collateral.errors?.remark"
                                    required
                                    placeholder="Remark"
                                />
                                <div v-if="props.errors?.[`collateral.${index}.remark`]" class="mt-1 text-sm text-red-500">
                                    {{ props.errors?.[`collateral.${index}.remark`] }}
                                </div>
                            </div>
                            <template v-else>
                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label for="ownership_no" class="text-base">
                                        Ownership No
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">{{ collateral.property.ownership_no }}</p>
                                    <Input
                                        v-else
                                        id="ownership_no"
                                        v-model="collateral.property.ownership_no"
                                        :error="errors?.[`property.${index}.ownership_no`]"
                                        placeholder="Ownership No"
                                    />
                                    <div v-if="props.errors?.[`collateral.${index}.property.ownership_no`]" class="mt-1 text-sm text-red-500">
                                        {{ props.errors?.[`collateral.${index}.property.ownership_no`] }}
                                    </div>
                                </div>
                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.selection_type_of_property_id`" class="text-base">
                                        Type of Property
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">{{ collateral.property.type_of_property_selection }}</p>
                                    <Select
                                        v-else
                                        v-model="collateral.property.selection_type_of_property_id"
                                        :error="errors?.[`property.${index}.selection_type_of_property_id`]"
                                    >
                                        <SelectTrigger class="w-full">
                                            <SelectValue placeholder="Select Type of Property" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="type in propertyTypes" :key="type.id" :value="type.id">
                                                {{ type.value }}
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <div
                                        v-if="props.errors?.[`collateral.${index}.property.selection_type_of_property_id`]"
                                        class="mt-1 text-sm text-red-500"
                                    >
                                        {{ props.errors?.[`collateral.${index}.property.selection_type_of_property_id`] }}
                                    </div>
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.lot_number`" class="text-base">
                                        Lot Number
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">{{ collateral.property.lot_number }}</p>
                                    <Input
                                        v-else
                                        :id="`property.${index}.lot_number`"
                                        v-model="collateral.property.lot_number"
                                        :error="errors?.[`property.${index}.lot_number`]"
                                        placeholder="Lot Number"
                                    />
                                    <div v-if="props.errors?.[`collateral.${index}.property.lot_number`]" class="mt-1 text-sm text-red-500">
                                        {{ props.errors?.[`collateral.${index}.property.lot_number`] }}
                                    </div>
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.selection_land_category_id`" class="text-base">
                                        Land Category
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">
                                        {{ collateral.property.land_category_selection }}
                                        <span v-if="collateral.property.land_category_other">({{ collateral.property.land_category_other }})</span>
                                    </p>
                                    <Select
                                        v-else
                                        v-model="collateral.property.selection_land_category_id"
                                        :error="errors?.[`property.${index}.selection_land_category_id`]"
                                    >
                                        <SelectTrigger class="w-full">
                                            <SelectValue placeholder="Select land category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="category in landCategories" :key="category.id" :value="category.id">
                                                {{ category.value }}
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <div
                                        v-if="props.errors?.[`collateral.${index}.property.selection_land_category_id`]"
                                        class="mt-1 text-sm text-red-500"
                                    >
                                        {{ props.errors?.[`collateral.${index}.property.selection_land_category_id`] }}
                                    </div>
                                    <Input
                                        v-if="collateral.property.selection_land_category_id === 22 && !isShow"
                                        class="mt-2"
                                        :id="`property.${index}.land_category_other`"
                                        placeholder="Remark"
                                        v-model="collateral.property.land_category_other"
                                        :error="props.errors?.[`collateral.${index}.property.selection_land_category_id`]"
                                    />
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.city`" class="text-base">
                                        City
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">{{ collateral.property.city }}</p>
                                    <Input
                                        v-else
                                        :id="`property.${index}.city`"
                                        v-model="collateral.property.city"
                                        :error="props.errors?.[`collateral.${index}.property.city`]"
                                        placeholder="City"
                                    />
                                    <div v-if="props.errors?.[`collateral.${index}.property.city`]" class="mt-1 text-sm text-red-500">
                                        {{ props.errors?.[`collateral.${index}.property.city`] }}
                                    </div>
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.location`" class="text-base">
                                        Location
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">{{ collateral.property.location }}</p>
                                    <Input
                                        v-else
                                        :id="`property.${index}.location`"
                                        v-model="collateral.property.location"
                                        :error="props.errors?.[`collateral.${index}.property.location`]"
                                        placeholder="Location"
                                    />
                                    <div v-if="props.errors?.[`collateral.${index}.property.location`]" class="mt-1 text-sm text-red-500">
                                        {{ props.errors?.[`collateral.${index}.property.location`] }}
                                    </div>
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.land_size`" class="text-base">
                                        Land Size
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">{{ collateral.property.land_size }} {{ collateral.property.land_size_unit_selection }}</p>
                                    <div v-else class="flex">
                                        <Input
                                            :id="`property.${index}.land_size`"
                                            class="w-full !rounded-r-none"
                                            v-model="collateral.property.land_size"
                                            :error="errors?.[`property.${index}.land_size`]"
                                            placeholder="Land Size"
                                        />
                                        <Select
                                            v-model="collateral.property.selection_land_size_unit"
                                            :error="errors?.[`property.${index}.selection_land_size_unit`]"
                                        >
                                            <SelectTrigger class="bg-cloud w-45 !rounded-l-none">
                                                <SelectValue placeholder="Unit" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in squareTypes" :key="type.id" :value="type.id">
                                                    {{ type.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div v-if="props.errors?.[`collateral.${index}.property.land_size`]" class="mt-1 text-sm text-red-500">
                                        {{ props.errors?.[`collateral.${index}.property.land_size`] }}
                                    </div>
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.district`" class="text-base">
                                        District
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">{{ collateral.property.district }}</p>
                                    <Input
                                        v-else
                                        :id="`property.${index}.district`"
                                        v-model="collateral.property.district"
                                        :error="props.errors?.[`collateral.${index}.property.district`]"
                                        placeholder="District"
                                    />
                                    <div v-if="props.errors?.[`collateral.${index}.property.district`]" class="mt-1 text-sm text-red-500">
                                        {{ props.errors?.[`collateral.${index}.property.district`] }}
                                    </div>
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.no_syit_piawai`" class="text-base">
                                        No. Syit Piawai
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">{{ collateral.property.no_syit_piawai }}</p>
                                    <Input
                                        v-else
                                        :id="`property.${index}.no_syit_piawai`"
                                        v-model="collateral.property.no_syit_piawai"
                                        :error="props.errors?.[`collateral.${index}.property.no_syit_piawai`]"
                                        placeholder="No. Syit Piawai"
                                    />
                                    <div v-if="props.errors?.[`collateral.${index}.property.no_syit_piawai`]" class="mt-1 text-sm text-red-500">
                                        {{ props.errors?.[`collateral.${index}.property.no_syit_piawai`] }}
                                    </div>
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.certified_plan_no`" class="text-base"> Certified Plan No </Label>
                                    <p v-if="isShow">{{ collateral.property.certified_plan_no }}</p>
                                    <Input
                                        v-else
                                        :id="`property.${index}.certified_plan_no`"
                                        v-model="collateral.property.certified_plan_no"
                                        :error="errors?.[`property.${index}.certified_plan_no`]"
                                        placeholder="Certified Plan No"
                                    />
                                    <div v-if="errors?.[`property.${index}.certified_plan_no`]" class="mt-1 text-sm text-red-500">
                                        {{ errors?.[`property.${index}.certified_plan_no`] }}
                                    </div>
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.selection_land_status_id`" class="text-base">
                                        Land Status
                                        <RequiredIndicator v-if="!isShow" />
                                    </Label>
                                    <p v-if="isShow">{{ collateral.property.land_status_selection }}</p>
                                    <Select
                                        v-else
                                        v-model="collateral.property.selection_land_status_id"
                                        :error="errors?.[`property.${index}.selection_land_status_id`]"
                                    >
                                        <SelectTrigger class="w-full">
                                            <SelectValue placeholder="Select land status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="status in landStatuses" :key="status.id" :value="status.id">
                                                {{ status.value }}
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <div
                                        v-if="props.errors?.[`collateral.${index}.property.selection_land_status_id`]"
                                        class="mt-1 text-sm text-red-500"
                                    >
                                        {{ props.errors?.[`collateral.${index}.property.selection_land_status_id`] }}
                                    </div>
                                </div>

                                <div :class="[isShow ? 'flex items-center justify-between px-1' : '']">
                                    <Label :for="`property.${index}.built_up_area_of_property`" class="text-base"> Built Up Area </Label>
                                    <p v-if="isShow">
                                        {{ collateral.property.built_up_area_of_property }}
                                        {{ collateral.property.built_up_area_unit_selection }}
                                    </p>
                                    <div v-else class="flex">
                                        <Input
                                            :id="`property.${index}.built_up_area_of_property`"
                                            v-model="collateral.property.built_up_area_of_property"
                                            :error="errors?.[`property.${index}.built_up_area_of_property`]"
                                            class="w-full !rounded-r-none"
                                            placeholder="Built Up Area"
                                        />
                                        <Select
                                            v-model="collateral.property.selection_built_up_area_unit"
                                            :error="errors?.[`property.${index}.selection_built_up_area_unit`]"
                                        >
                                            <SelectTrigger class="bg-cloud w-45 !rounded-l-none">
                                                <SelectValue placeholder="Unit" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="type in squareTypes" :key="type.id" :value="type.id">
                                                    {{ type.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div
                                        v-if="props.errors?.[`collateral.${index}.property.built_up_area_of_property`]"
                                        class="mt-1 text-sm text-red-500"
                                    >
                                        {{ props.errors?.[`collateral.${index}.property.built_up_area_of_property`] }}
                                    </div>
                                </div>
                                <Separator class="col-span-2" v-if="isShow" />
                                <!-- Address Line 1 -->
                                <div v-if="isShow" class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                    <div class="px-1">
                                        <Label for="address" class="py-0 text-base">Address </Label>
                                        <p class="whitespace-pre-wrap">{{ formatAddress(collateral.property.address) }}</p>
                                    </div>
                                </div>
                                <template v-else>
                                    <div class="col-span-2">
                                        <Label :for="`property.${index}.address.line_1`" class="text-base">
                                            Address Line 1
                                            <RequiredIndicator v-if="!isShow" />
                                        </Label>
                                        <Input
                                            :id="`property.${index}.address.line_1`"
                                            v-model="collateral.property.address.line_1"
                                            :error="errors?.[`property.${index}.address.line_1`]"
                                            placeholder="Address Line 1"
                                        />
                                        <div v-if="props.errors?.[`collateral.${index}.property.address.line_1`]" class="mt-1 text-sm text-red-500">
                                            {{ props.errors?.[`collateral.${index}.property.address.line_1`] }}
                                        </div>
                                    </div>

                                    <!-- Address Line 2 -->
                                    <div class="col-span-2">
                                        <Label :for="`property.${index}.address.line_2`" class="text-base"> Address Line 2 </Label>
                                        <Input
                                            :id="`property.${index}.address.line_2`"
                                            v-model="collateral.property.address.line_2"
                                            :error="errors?.[`property.${index}.address.line_2`]"
                                            placeholder="Address Line 2"
                                        />
                                    </div>

                                    <!-- Postcode -->
                                    <div>
                                        <Label :for="`property.${index}.address.postcode`" class="text-base">
                                            Postcode
                                            <RequiredIndicator v-if="!isShow" />
                                        </Label>
                                        <Input
                                            :id="`property.${index}.address.postcode`"
                                            v-model="collateral.property.address.postcode"
                                            :error="props.errors?.[`collateral.${index}.property.address.postcode`]"
                                            placeholder="Postcode"
                                        />
                                        <div v-if="props.errors?.[`collateral.${index}.property.address.postcode`]" class="mt-1 text-sm text-red-500">
                                            {{ props.errors?.[`collateral.${index}.property.address.postcode`] }}
                                        </div>
                                    </div>

                                    <!-- City -->
                                    <div>
                                        <Label :for="`property.${index}.address.city`" class="text-base">
                                            City
                                            <RequiredIndicator v-if="!isShow" />
                                        </Label>
                                        <Input
                                            :id="`property.${index}.address.city`"
                                            v-model="collateral.property.address.city"
                                            :error="props.errors?.[`collateral.${index}.property.address.city`]"
                                            placeholder="City"
                                        />
                                        <div v-if="props.errors?.[`collateral.${index}.property.address.city`]" class="mt-1 text-sm text-red-500">
                                            {{ props.errors?.[`collateral.${index}.property.address.city`] }}
                                        </div>
                                    </div>

                                    <!-- State -->
                                    <div>
                                        <Label :for="`property.${index}.address.selection_state_id`" class="text-base">
                                            State
                                            <RequiredIndicator v-if="!isShow" />
                                        </Label>
                                        <FormSelect
                                            :id="`property.${index}.address.selection_state_id`"
                                            label=""
                                            :model-value="collateral.property.address.selection_state_id"
                                            @update:model-value="collateral.property.address.selection_state_id = $event"
                                            :options="states"
                                            placeholder="Select state"
                                            :required="!isShow"
                                            :error="props.errors?.[`collateral.${index}.property.address.selection_state_id`]"
                                            :filter-by-country="true"
                                            :selected-country-id="collateral.property.address.selection_country_id"
                                            labelClass="hidden"
                                        />
                                    </div>

                                    <!-- Country -->
                                    <div>
                                        <Label :for="`property.${index}.address.selection_country_id`" class="text-base">
                                            Country
                                            <RequiredIndicator v-if="!isShow" />
                                        </Label>
                                        <Select
                                            v-model="collateral.property.address.selection_country_id"
                                            :error="props.errors?.[`collateral.${index}.property.address.selection_country_id`]"
                                        >
                                            <SelectTrigger class="w-full">
                                                <SelectValue placeholder="Select country" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="country in countries" :key="country.id" :value="country.id">
                                                    {{ country.value }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <div
                                            v-if="props.errors?.[`collateral.${index}.property.address.selection_country_id`]"
                                            class="mt-1 text-sm text-red-500"
                                        >
                                            {{ props.errors?.[`collateral.${index}.property.address.selection_country_id`] }}
                                        </div>
                                    </div>
                                </template>
                            </template>
                        </div>
                        <CardContent v-if="isShow" class="px-0 py-4">
                            <Label class="text-[20px]">Valuation</Label>
                            <Accordion type="single" class="w-full" collapsible v-model="openValuersAccordion">
                                <AccordionItem v-for="(valuer, index) in collateral.valuers" :key="index" :value="String(index)" class="mb-1">
                                    <Card class="gap-0 rounded-xs py-0">
                                        <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                            <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                <FaIcon name="plus" />
                                            </span>
                                            <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                <FaIcon name="minus" />
                                            </span>
                                            <span class="flex-1 py-2 text-left font-medium"> {{ ordinal(index + 1) }} Valuation </span>
                                        </AccordionTrigger>

                                        <Separator />
                                        <AccordionContent class="p-2">
                                            <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
                                                <div class="flex items-center justify-between px-1">
                                                    <Label :for="`valuers.${index}.valuer`" class="text-base">Valuer </Label>
                                                    {{ valuer.valuer }}
                                                </div>

                                                <div class="flex items-center justify-between px-1">
                                                    <Label :for="`valuers.${index}.valuation_amount`" class="text-base">Valuation (RM) </Label>
                                                    {{ valuer.valuation_amount }}
                                                </div>

                                                <div class="flex items-center justify-between px-1">
                                                    <Label :for="`valuers.${index}.valuation_received_date`" class="text-base"
                                                        >Valuation Received Date</Label
                                                    >
                                                    {{ valuer.valuation_received_date ? formatDate(valuer.valuation_received_date) : '-' }}
                                                </div>

                                                <div class="flex items-center justify-between px-1">
                                                    <Label :for="`valuers.${index}.land_search_received_date`" class="text-base"
                                                        >Land Search Received Date</Label
                                                    >
                                                    {{ valuer.land_search_received_date ? formatDate(valuer.land_search_received_date) : '-' }}
                                                </div>
                                            </div>
                                        </AccordionContent>
                                    </Card>
                                </AccordionItem>
                            </Accordion>
                        </CardContent>
                        <Valuation
                            v-else
                            :valuers="collateral.valuers"
                            :errors="
                                Object.fromEntries(
                                    Object.entries(props.errors ?? {})
                                        .filter(([key]) => key.startsWith(`collateral.${index}.valuers.`))
                                        .map(([key, val]) => [key.replace(`collateral.${index}.`, ''), val]),
                                )
                            "
                            :ordinal="ordinal"
                        />
                        <CardContent v-if="isShow" class="px-0 py-4">
                            <Label class="text-[20px]">Additional Owner</Label>
                            <Accordion type="single" class="w-full" collapsible v-model="openOwnersAccordion">
                                <AccordionItem v-for="(owner, index) in collateral.owners" :key="index" :value="String(index)" class="mb-1">
                                    <Card class="gap-0 rounded-xs py-0">
                                        <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                            <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                <FaIcon name="plus" />
                                            </span>
                                            <!-- Minus icon: visible when open -->
                                            <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                <FaIcon name="minus" />
                                            </span>
                                            <span class="flex-1 text-left font-medium"> {{ ordinal(index + 2) }} Owner </span>
                                        </AccordionTrigger>
                                        <Separator />
                                        <AccordionContent class="p-2">
                                            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                                                <div class="space-y-3">
                                                    <div class="flex items-center justify-between px-1">
                                                        <Label :for="`property_owners.${index}.name`" class="text-base">Name</Label>
                                                        {{ owner.name }}
                                                    </div>

                                                    <div class="flex items-center justify-between px-1">
                                                        <Label :for="`property_owners.${index}.identity_no`" class="text-base">I/C No</Label>
                                                        {{ owner.identity_no || '-' }}
                                                    </div>

                                                    <div class="flex items-center justify-between px-1">
                                                        <Label :for="`property_owners.${index}.telephone`" class="text-base">Telephone</Label>
                                                        {{ owner.telephone ? `${owner.telephone_country_selection} ${owner.telephone}` : '-' }}
                                                    </div>

                                                    <div class="flex items-center justify-between px-1">
                                                        <Label :for="`property_owners.${index}.mobile_phone`" class="text-base">Mobile Phone</Label>
                                                        {{ owner.mobile_phone ? `${owner.mobile_country_selection} ${owner.mobile_phone}` : '-' }}
                                                    </div>
                                                </div>
                                                <div class="space-y-3">
                                                    <div>
                                                        <Label :for="`property_owners.${index}.address.line_1`" class="pb-2 text-base">Address</Label>
                                                        <p class="whitespace-pre-wrap">
                                                            {{ formatAddress(owner.address) || '-' }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </AccordionContent>
                                    </Card>
                                </AccordionItem>
                            </Accordion>
                        </CardContent>
                        <AdditionalPropertyOwner
                            v-else
                            :property_owners="collateral.property_owners"
                            :mobileCountries="mobileCountries"
                            :telephoneCountries="telephoneCountries"
                            :errors="
                                Object.fromEntries(
                                    Object.entries(props.errors ?? {})
                                        .filter(([key]) => key.startsWith(`collateral.${index}.property_owners.`))
                                        .map(([key, val]) => [key.replace(`collateral.${index}.`, ''), val]),
                                )
                            "
                            :states="states"
                            :countries="countries"
                            :ordinal="ordinal"
                            isEdit
                        />
                    </AccordionContent>
                </Card>
            </AccordionItem>
        </Accordion>
    </CardContent>
</template>
