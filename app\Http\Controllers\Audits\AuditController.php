<?php

namespace App\Http\Controllers\Audits;

use App\Http\Controllers\Controller;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use OwenIt\Auditing\Models\Audit;

class AuditController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Transform audit data for response
     */
    private function transformAudit(Audit $audit, bool $includeTags = false): array
    {
        // Get model name from auditable_type
        $modelName = class_basename($audit->auditable_type);

        $data = [
            'id' => $audit->id,
            'user' => $audit->user ? [
                'id' => $audit->user->id,
                'username' => $audit->user->username,
                'email' => $audit->user->email,
            ] : null,
            'event' => $audit->event,
            'auditable_type' => $audit->auditable_type,
            'auditable_id' => $audit->auditable_id,
            'model_name' => $modelName,
            'old_values' => $audit->old_values,
            'new_values' => $audit->new_values,
            'url' => $audit->url,
            'ip_address' => $audit->ip_address,
            'user_agent' => $audit->user_agent,
            'created_at' => $audit->created_at,
        ];

        if ($includeTags) {
            $data['tags'] = $audit->tags;
        }

        return $data;
    }

    /**
     * Get unique auditable types for filtering
     */
    private function getAuditableTypes(): array
    {
        return Audit::select('auditable_type')
            ->distinct()
            ->orderBy('auditable_type')
            ->pluck('auditable_type')
            ->map(function ($type) {
                return [
                    'value' => $type,
                    'label' => class_basename($type),
                ];
            })
            ->toArray();
    }

    /**
     * Get unique events for filtering
     */
    private function getEvents(): array
    {
        return Audit::select('event')
            ->distinct()
            ->orderBy('event')
            ->pluck('event')
            ->map(function ($event) {
                return [
                    'value' => $event,
                    'label' => ucfirst($event),
                ];
            })
            ->toArray();
    }

    /**
     * Display a listing of the audits.
     */
    public function index(Request $request): Response
    {
        // Authorize access
        $this->authorize('viewAny', Audit::class);

        $query = Audit::query()
            ->when($request->input('event'), function ($query, $event) {
                $query->where('event', $event);
            })
            ->when($request->input('user_id'), function ($query, $userId) {
                $query->where('user_id', $userId);
            })
            ->when($request->input('auditable_type'), function ($query, $auditableType) {
                $query->where('auditable_type', $auditableType);
            })
            ->when($request->input('from_date'), function ($query, $fromDate) {
                $query->whereDate('created_at', '>=', $fromDate);
            })
            ->when($request->input('to_date'), function ($query, $toDate) {
                $query->whereDate('created_at', '<=', $toDate);
            })
            ->with(['user:id,username,email']);

        $this->applySorting($query, $request, 'created_at', 'desc');

        $audits = $this->applyPagination($query, $request, 10,
            fn ($audit) => $this->transformAudit($audit));

        return Inertia::render('audits/Index', [
            'audits' => $audits,
            'auditableTypes' => $this->getAuditableTypes(),
            'events' => $this->getEvents(),
            'filters' => $request->only([
                'event',
                'user_id',
                'auditable_type',
                'from_date',
                'to_date',
                'per_page',
                'sort_field',
                'sort_direction',
            ]),
        ]);
    }

    /**
     * Display the specified audit.
     */
    public function show(Audit $audit): Response
    {
        // Authorize access
        $this->authorize('view', $audit);

        // Load related user
        $audit->load(['user:id,username,email']);

        return Inertia::render('audits/Show', [
            'audit' => $this->transformAudit($audit, true),
        ]);
    }
}
