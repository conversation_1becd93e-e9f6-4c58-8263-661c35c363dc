<?php

namespace App\Http\Requests\Companies;

use App\Enums\Company\CompanyStatus;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateCompanyStatusRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => ['required', new Enum(CompanyStatus::class)],
        ];
    }
}
