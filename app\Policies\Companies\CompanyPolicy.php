<?php

namespace App\Policies\Companies;

use App\Enums\AccessControl\PermissionName;
use App\Models\Company;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CompanyPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any companies.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_COMPANIES->value);
    }

    /**
     * Determine whether the user can view the company.
     *
     * @return bool
     */
    public function view(User $user, Company $company)
    {
        return $user->hasPermissionTo(PermissionName::READ_COMPANIES->value);
    }

    /**
     * Determine whether the user can create companies.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_COMPANIES->value);
    }

    /**
     * Determine whether the user can update the company.
     *
     * @return bool
     */
    public function update(User $user, Company $company)
    {
        return $user->hasPermissionTo(PermissionName::UPDATE_COMPANIES->value);
    }

    /**
     * Determine whether the user can delete the company.
     *
     * @return bool
     */
    public function delete(User $user, Company $company)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_COMPANIES->value);
    }

    /**
     * Determine whether the user can restore the company.
     *
     * @return bool
     */
    public function restore(User $user, Company $company)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_COMPANIES->value);
    }

    /**
     * Determine whether the user can permanently delete the company.
     *
     * @return bool
     */
    public function forceDelete(User $user, Company $company)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_COMPANIES->value);
    }
}
