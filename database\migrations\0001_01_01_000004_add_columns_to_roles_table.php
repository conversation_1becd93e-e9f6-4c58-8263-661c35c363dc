<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->integer('level')->default(0)->after('name');
            $table->boolean('can_see_same_level')->default(false)->after('level');
            $table->boolean('is_required_headquarter')->default(true)->after('can_see_same_level');
            $table->boolean('is_required_company')->default(true)->after('is_required_headquarter');
            $table->boolean('is_required_team')->default(true)->after('is_required_company');
            $table->boolean('is_headquarter')->default(false)->after('is_required_team');
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
        });
    }

    public function down()
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropForeign(['updated_by']);
            $table->dropColumn(['level', 'can_see_same_level', 'is_required_headquarter', 'is_required_company', 'is_required_team', 'is_headquarter', 'created_by', 'updated_by']);
        });
    }
};
