<?php

namespace App\Policies\Agents;

use App\Enums\AccessControl\PermissionName;
use App\Enums\AccessControl\RoleName;
use App\Models\AgentProfile;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class AgentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any agents.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_AGENTS->value);
    }

    /**
     * Determine whether the user can view the agent.
     *
     * @return bool
     */
    public function view(User $user, AgentProfile $agent)
    {
        return $user->hasPermissionTo(PermissionName::READ_AGENTS->value);
    }

    /**
     * Determine whether the user can create agents.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_AGENTS->value);
    }

    /**
     * Determine whether the user can update the agent.
     *
     * @return bool
     */
    public function update(User $user, AgentProfile $agent)
    {
        if (! $user->hasCompanyAccess($agent->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_AGENTS->value);
    }

    /**
     * Determine whether the user can delete the agent.
     *
     * @return bool
     */
    public function delete(User $user, AgentProfile $agent)
    {
        if (! $user->hasCompanyAccess($agent->company_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::DELETE_AGENTS->value);
    }

    /**
     * Determine whether the user can restore the agent.
     *
     * @return bool
     */
    public function restore(User $user, AgentProfile $agent)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_AGENTS->value);
    }

    /**
     * Determine whether the user can permanently delete the agent.
     *
     * @return bool
     */
    public function forceDelete(User $user, AgentProfile $agent)
    {
        // Only Super Administrator can force delete agents
        return $user->hasRole(RoleName::SUPER_ADMIN->value);
    }
}
