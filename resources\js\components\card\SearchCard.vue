<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface StatusOptions {
    value: string | number;
    label: string;
}

interface Props {
    searchValue: string;
    searchStatus?: string | number;
    searchLabel?: string;
    searchPlaceholder?: string;
    resetLabel?: string;
    searchButtonLabel?: string;
    status?: boolean;
    statusValue: string | number;
    statusOptions?: StatusOptions[];
}

const props = withDefaults(defineProps<Props>(), {
    searchLabel: 'Search',
    searchPlaceholder: 'Search...',
    resetLabel: 'Reset',
    searchButtonLabel: 'Search',
    superAdmin: false,
    status: false,
    statusOptions: () => [],
});

const emit = defineEmits(['search', 'reset', 'update:searchValue', 'update:searchStatus']);

// Handle search button click
function onSearch() {
    emit('search', props.searchValue);
}

// Handle Enter key press
function onKeyDown(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        onSearch();
    }
}
</script>

<template>
    <Card class="py-4">
        <CardContent class="grid grid-cols-3 gap-4">
            <div class="w-full">
                <Label class="pb-2" for="search-input">{{ searchLabel }}</Label>
                <Input
                    id="search-input"
                    :placeholder="searchPlaceholder"
                    :model-value="searchValue"
                    @update:model-value="(value) => emit('update:searchValue', value)"
                    @keydown="onKeyDown"
                    class="w-full"
                />
            </div>
            <div v-if="status">
                <Label class="pb-2" for="status">Status</Label>
                <Select :model-value="props.statusValue" @update:model-value="emit('update:searchStatus', Number($event))">
                    <SelectTrigger class="w-full">
                        <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem v-for="option in statusOptions" :key="option.value" :value="option.value">
                            {{ option.label }}
                        </SelectItem>
                    </SelectContent>
                </Select>
            </div>
            <slot name="additional-filters"></slot>
        </CardContent>
        <CardFooter class="flex flex-col items-end gap-2 px-2 sm:flex-row sm:justify-end sm:px-6">
            <Button variant="outline" @click="$emit('reset')" class="bg-card text-muted-foreground flex items-center gap-2">
                <FaIcon name="arrow-rotate-right" />
                {{ resetLabel }}
            </Button>
            <Button @click="onSearch" class="bg-cobalt hover:bg-cobalt-hover flex items-center gap-2 whitespace-nowrap hover:text-white">
                <FaIcon name="magnifying-glass" />
                {{ searchButtonLabel }}
            </Button>
            <slot name="additional-buttons"></slot>
        </CardFooter>
    </Card>
</template>
