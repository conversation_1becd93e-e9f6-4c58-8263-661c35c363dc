<?php

namespace App\Enums\Loan;

use App\Traits\EnumTrait;

enum LoanTxnType: int
{
    use EnumTrait;

    case INSTALLMENT = 1;
    case LATE_INTEREST = 2;
    case LEGAL_FEE = 3;
    case MISC_CHARGE = 4;
    case POSTAGE = 5;

    /**
     * Get all available transaction types as an array
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return [
            self::INSTALLMENT->value => 'Installment',
            self::LATE_INTEREST->value => 'Late Interest',
            self::LEGAL_FEE->value => 'Legal Fee',
            self::MISC_CHARGE->value => 'Misc Charge',
            self::POSTAGE->value => 'Postage',
        ];
    }

    /**
     * Get the display name for a transaction type
     */
    public function label(): string
    {
        return match ($this) {
            self::INSTALLMENT => 'Installment',
            self::LATE_INTEREST => 'Late Interest',
            self::LEGAL_FEE => 'Legal Fee',
            self::MISC_CHARGE => 'Misc Charge',
            self::POSTAGE => 'Postage',
        };
    }

    /**
     * Get the description for a transaction type
     */
    public function description(): string
    {
        return match ($this) {
            self::INSTALLMENT => 'Regular loan installment payment',
            self::LATE_INTEREST => 'Interest charged for late payment',
            self::LEGAL_FEE => 'Legal fees for collection activities',
            self::MISC_CHARGE => 'Miscellaneous charges',
            self::POSTAGE => 'Postage and handling charges',
        };
    }
}
