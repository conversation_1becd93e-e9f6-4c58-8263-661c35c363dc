<?php

namespace App\Http\Resources\Companies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $this->code,
            'name' => $this->name,
            'display_name' => $this->display_name,
            'business_registration_no' => $this->business_registration_no,
            'old_business_registration_no' => $this->old_business_registration_no,
            'status' => $this->status,
            'headquarter_id' => $this->headquarter_id,
            'remark' => $this->remark,
            'headquarter' => $this->whenLoaded('headquarter', fn () => [
                'id' => $this->headquarter->id,
                'code' => $this->headquarter->code,
                'display_name' => $this->headquarter->display_name,
            ]),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];
    }
}
