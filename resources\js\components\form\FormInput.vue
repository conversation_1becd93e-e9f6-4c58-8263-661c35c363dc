<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface Props {
    id: string;
    label: string;
    modelValue: string;
    placeholder?: string;
    required?: boolean;
    maxlength?: string | number;
    error?: string;
    class?: string;
    labelClass?: string;
    inputClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '',
    required: false,
    maxlength: 255,
    class: 'gap-2',
    labelClass: '',
    inputClass: '',
});

const emit = defineEmits(['update:modelValue']);
</script>

<template>
    <div :class="props.class">
        <Label :for="props.id" :class="props.labelClass">
            {{ props.label }}
            <RequiredIndicator v-if="props.required" />
        </Label>
        <Input
            :id="props.id"
            :model-value="props.modelValue"
            @update:model-value="(value) => emit('update:modelValue', value)"
            :required="props.required"
            :maxlength="props.maxlength"
            :placeholder="props.placeholder"
            :class="cn('mt-1', props.inputClass)"
        />
        <InputError class="mt-1" :message="props.error" />
    </div>
</template>
