[supervisord]
nodaemon=true

[program:truncate-logs]
command=/bin/bash -c "find /var/log/{supervisor,laravel,php,nginx} -name '*.log' -exec truncate -s 0 {} +"
autostart=true
autorestart=false
startsecs=0
priority=1
stdout_logfile=/var/log/supervisor/truncate.log
stderr_logfile=/var/log/supervisor/truncate.err.log

[program:migrate]
command=/opt/bitnami/php/bin/php artisan migrate --seed
user=daemon
autostart=true
autorestart=false
priority=2
stdout_logfile=/var/log/supervisor/migrate.log
stderr_logfile=/var/log/supervisor/migrate.err.log

[program:php-fpm]
command=/opt/bitnami/php/sbin/php-fpm -F
autostart=true
autorestart=true
priority=3
stdout_logfile=/var/log/supervisor/php-fpm.log
stderr_logfile=/var/log/supervisor/php-fpm.err.log

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
priority=4
stdout_logfile=/var/log/supervisor/nginx.log
stderr_logfile=/var/log/supervisor/nginx.err.log

[program:laravel-queue]
command=/opt/bitnami/php/bin/php artisan queue:work --sleep=3 --tries=3
directory=/var/www
autostart=true
autorestart=true
priority=5
stdout_logfile=/var/log/supervisor/laravel-queue.log
stderr_logfile=/var/log/supervisor/laravel-queue.err.log

[program:laravel-scheduler]
command=/opt/bitnami/php/bin/php artisan schedule:work
directory=/var/www
autostart=true
autorestart=true
priority=6
stdout_logfile=/var/log/supervisor/laravel-scheduler.log
stderr_logfile=/var/log/supervisor/laravel-scheduler.err.log
