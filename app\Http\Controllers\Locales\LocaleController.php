<?php

namespace App\Http\Controllers\Locales;

use App\Enums\Locale\LocaleStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Locales\StoreLocaleRequest;
use App\Http\Requests\Locales\UpdateLocaleRequest;
use App\Http\Requests\Locales\UpdateStatusRequest;
use App\Http\Resources\Locales\LocaleResource;
use App\Models\Locale;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;

class LocaleController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Display a listing of the locales.
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Locale::class);

        $query = Locale::query()->withAuditUsers();

        $this->applySearchFilter($query, $request, 'word');
        $this->applySearchFilter($query, $request, 'en');
        $this->applySearchFilter($query, $request, 'zh');
        $this->applySorting($query, $request);

        $locales = $this->applyPagination($query, $request, 10,
            fn ($locale) => (new LocaleResource($locale))->toArray($request));

        return Inertia::render('locales/Index', [
            'locales' => $locales,
            'filters' => $request->only(['word', 'en', 'zh', 'per_page', 'sort_field', 'sort_direction']),
        ]);
    }

    /**
     * Show the form for creating a new locale.
     *
     * @return \Inertia\Response
     */
    public function create()
    {
        $this->authorize('create', Locale::class);

        return Inertia::render('locales/Create', [
            'statuses' => LocaleStatus::options(),
            'defaultStatus' => LocaleStatus::ACTIVE,
        ]);
    }

    /**
     * Store a newly created locale in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreLocaleRequest $request)
    {
        $this->authorize('create', Locale::class);

        try {
            Locale::create($request->validated());

            return redirect()->route('locales.index')->with('message', 'Locale created successfully');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Failed to create locale. '.$e->getMessage());
        }
    }

    /**
     * Display the specified locale.
     *
     * @return \Inertia\Response
     */
    public function show(Locale $locale)
    {
        $this->authorize('view', $locale);

        $locale->withAuditUsers();

        return Inertia::render('locales/Show', [
            'locale' => (new LocaleResource($locale))->toArray(request()),
        ]);
    }

    /**
     * Show the form for editing the specified locale.
     *
     * @return \Inertia\Response
     */
    public function edit(Locale $locale)
    {
        $this->authorize('update', $locale);

        return Inertia::render('locales/Edit', [
            'locale' => $locale,
            'statuses' => LocaleStatus::options(),
        ]);
    }

    /**
     * Update the specified locale in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(UpdateLocaleRequest $request, Locale $locale)
    {
        $this->authorize('update', $locale);

        try {
            $locale->update($request->validated());

            return redirect()->route('locales.index')->with('message', 'Locale updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Failed to update locale. '.$e->getMessage());
        }
    }

    /**
     * Remove the specified locale from storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Locale $locale)
    {
        $this->authorize('delete', $locale);

        try {
            $locale->delete();

            return redirect()->route('locales.index')->with('message', 'Locale deleted successfully');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Failed to delete locale. '.$e->getMessage());
        }
    }

    /**
     * Update the status of the specified resource.
     */
    public function updateStatus(UpdateStatusRequest $request, Locale $locale): RedirectResponse
    {
        $this->authorize('update', $locale);

        try {
            $locale->update(['status' => $request->validated('status')]);

            return Redirect::back()->with('success', 'locale status updated successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to update locale status. '.$e->getMessage());
        }
    }
}
