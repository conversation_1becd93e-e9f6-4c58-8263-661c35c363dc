<?php

namespace Database\Factories;

use App\Enums\Company\CompanyStatus;
use App\Models\Company;
use App\Models\Headquarter;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Company::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $displayName = fake()->company();

        return [
            'uuid' => Str::uuid(),
            'code' => 'CO'.strtoupper(Str::random(6)),
            'display_name' => $displayName,
            'name' => strtoupper($displayName),
            'status' => CompanyStatus::ACTIVE,
            'headquarter_id' => Headquarter::factory(),
            'remark' => fake()->optional(0.7)->sentence(),
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Indicate that the company is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => CompanyStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the company is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => CompanyStatus::INACTIVE,
        ]);
    }

    /**
     * Set a specific parent headquarter for this company.
     */
    public function forHeadquarter(Headquarter $headquarter): static
    {
        return $this->state(fn (array $attributes) => [
            'headquarter_id' => $headquarter->id,
        ]);
    }
}
