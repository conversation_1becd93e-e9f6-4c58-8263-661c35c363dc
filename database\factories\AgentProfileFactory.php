<?php

namespace Database\Factories;

use App\Enums\Agent\AgentStatus;
use App\Models\AgentProfile;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgentProfile>
 */
class AgentProfileFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AgentProfile::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $displayName = fake()->name();

        return [
            'uuid' => Str::uuid(),
            'code' => 'AGT'.strtoupper(Str::random(6)),
            'company_id' => Company::factory(),
            'name' => $displayName,
            'display_name' => strtoupper($displayName),
            'email' => fake()->unique()->safeEmail(),
            'status' => AgentStatus::ACTIVE,
            'remark' => fake()->optional(0.7)->sentence(),
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Indicate that the agent is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => AgentStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the agent is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => AgentStatus::INACTIVE,
        ]);
    }
}
