<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import Heading from '@/components/Heading.vue';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/composables/useAuth';
import AppLayout from '@/layouts/AppLayout.vue';
import { Outcome } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';

const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess } = useAuth();

interface Props {
    agentOutcome: Outcome;
}

const props = defineProps<Props>();
const form = useForm({});
</script>

<template>
    <AppLayout>
        <Head :title="`Agent Outcome: ${props.agentOutcome.id}`" />
        <div class="px-4 py-3">
            <Heading title="Agent Outcome" pageNumber="P000031" description="View details of the agent outcome record" />
            <AppCard title="View Agent Outcome" backRoute="agent-outcomes.index" :form="form" :itemId="props.agentOutcome.id">
                <dl class="grid grid-cols-2 gap-x-8">
                    <div class="grid grid-cols-3 py-3" v-if="!hasHeadquarterAccess">
                        <Label class="font-medium">Headquarter Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.agentOutcome.headquarter.display_name }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3" v-if="!hasCompanyAccess && !isHeadquarter">
                        <Label class="font-medium">Company Name</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.agentOutcome.company.display_name }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Agent Name</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.agentOutcome.agent?.display_name || '-' }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Outcome Type</Label>
                        <dd class="col-span-2 text-right text-sm">{{ props.agentOutcome.outcomeType }}</dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Amount</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.agentOutcome.amount }}
                        </dd>
                    </div>
                    <div class="grid grid-cols-3 py-3">
                        <Label class="font-medium">Remark</Label>
                        <dd class="col-span-2 text-right text-sm">
                            {{ props.agentOutcome.remark }}
                        </dd>
                    </div>
                </dl>
                <template #footer></template>
            </AppCard>
        </div>
    </AppLayout>
</template>
