<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\Contact;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Contact>
 */
class ContactFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Contact::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'contactable_type' => Company::class,
            'contactable_id' => Company::factory(),
            'category' => 4, // Other
            'selection_type_id' => null,
            'type' => null,
            'selection_country_id' => null,
            'country' => null,
            'contact' => fake()->phoneNumber(),
            'is_primary' => false,
            'can_receive_sms' => false,
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Configure the model factory to be a telephone contact.
     */
    public function telephone(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => 0, // Contact::CATEGORY_TELEPHONE
                'contact' => fake()->phoneNumber(),
            ];
        });
    }

    /**
     * Configure the model factory to be a mobile contact.
     */
    public function mobile(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => 1, // Contact::CATEGORY_MOBILE
                'contact' => fake()->phoneNumber(),
                'can_receive_sms' => true,
            ];
        });
    }

    /**
     * Configure the model factory to be an email contact.
     */
    public function email(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => 2, // Contact::CATEGORY_EMAIL
                'contact' => fake()->email(),
            ];
        });
    }

    /**
     * Configure the model factory to associate with a specific model.
     */
    public function forModel(string $modelType, int $modelId): static
    {
        return $this->state(fn (array $attributes) => [
            'contactable_type' => $modelType,
            'contactable_id' => $modelId,
        ]);
    }

    /**
     * Set the contact as primary.
     */
    public function primary(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_primary' => true,
        ]);
    }
}
