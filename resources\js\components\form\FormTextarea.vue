<script setup lang="ts">
import InputError from '@/components/InputError.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import '@vueform/multiselect/themes/default.css';

interface Props {
    id: string;
    label: string;
    modelValue: string;
    placeholder?: string;
    required?: boolean;
    error?: string;
    class?: string;
    textareaClass?: string;
    labelClass?: string;
    remark?: string;
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: '',
    required: false,
    class: 'gap-3',
    labelClass: '',
    remark: '',
});

const emit = defineEmits(['update:modelValue']);
</script>

<template>
    <Label :for="props.id" :class="props.labelClass">
        {{ props.label }}
        <RequiredIndicator v-if="props.required" />
    </Label>
    <Textarea
        :id="props.id"
        :model-value="props.modelValue"
        @update:model-value="(value: string) => emit('update:modelValue', value)"
        :required="props.required"
        :placeholder="props.placeholder"
        :class="cn('mt-1', props.textareaClass)"
    />
    <p v-if="props.remark !== ''" class="text-muted-foreground text-sm">
        {{ props.remark }}
    </p>
    <InputError class="mt-1" :message="props.error" />
</template>
