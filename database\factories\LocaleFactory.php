<?php

namespace Database\Factories;

use App\Enums\Locale\LocaleStatus;
use App\Models\Locale;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Locale>
 */
class LocaleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Locale::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $word = fake()->unique()->word();

        return [
            'uuid' => Str::uuid(),
            'word' => $word,
            'en' => fake()->sentence(),
            'zh' => fake()->optional(0.8)->sentence(),
            'status' => LocaleStatus::ACTIVE,
            'created_by' => null,
            'updated_by' => null,
            'deleted_by' => null,
        ];
    }

    /**
     * Indicate that the locale is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LocaleStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the locale is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => LocaleStatus::INACTIVE,
        ]);
    }
}
