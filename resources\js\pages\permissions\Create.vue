<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

const form = useForm({
    name: '',
});

const formFields = computed(() => [
    {
        id: 'name',
        label: 'Permission Name',
        type: 'input',
        required: true,
        placeholder: 'Permission Name',
        error: form.errors.name,
        modelValue: form.name,
        updateValue: (value: string) => (form.name = value),
    },
]);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('permissions.store'),
            entityName: 'permission',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Create Permission" />
        <div class="px-4 py-3">
            <Heading title="Permission" pageNumber="P000050" description="Create a new permission record" />

            <AppCard title="Add New Permission" :form="form" backRoute="permissions.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
