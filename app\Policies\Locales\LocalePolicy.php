<?php

namespace App\Policies\Locales;

use App\Enums\AccessControl\PermissionName;
use App\Models\Locale;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LocalePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any locales.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_LOCALES->value);
    }

    /**
     * Determine whether the user can view the locale.
     *
     * @return bool
     */
    public function view(User $user, Locale $locale)
    {
        return $user->hasPermissionTo(PermissionName::READ_LOCALES->value);
    }

    /**
     * Determine whether the user can create locales.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_LOCALES->value);
    }

    /**
     * Determine whether the user can update the locale.
     *
     * @return bool
     */
    public function update(User $user, Locale $locale)
    {
        return $user->hasPermissionTo(PermissionName::UPDATE_LOCALES->value);
    }

    /**
     * Determine whether the user can delete the locale.
     *
     * @return bool
     */
    public function delete(User $user, Locale $locale)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_LOCALES->value);
    }

    /**
     * Determine whether the user can restore the locale.
     *
     * @return bool
     */
    public function restore(User $user, Locale $locale)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_LOCALES->value);
    }

    /**
     * Determine whether the user can permanently delete the locale.
     *
     * @return bool
     */
    public function forceDelete(User $user, Locale $locale)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_LOCALES->value);
    }
}
