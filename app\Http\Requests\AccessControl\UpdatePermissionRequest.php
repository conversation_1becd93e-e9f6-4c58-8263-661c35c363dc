<?php

namespace App\Http\Requests\AccessControl;

use App\Enums\AccessControl\PermissionName;
use App\Http\Requests\BaseRequest;

class UpdatePermissionRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                'unique:permissions,name,'.$this->permission->id,
                'regex:/^[a-z]+\s[a-z-]+$/',
                function ($attribute, $value, $fail) {
                    // Check if the permission already exists in the enum
                    if ($value !== $this->permission->name && in_array($value, PermissionName::values())) {
                        $fail('This permission is already defined in the system.');
                    }
                },
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'The permission name is required.',
            'name.unique' => 'This permission name already exists.',
            'name.regex' => 'Permission must be in format "action resource" (e.g., "create users").',
        ];
    }
}
