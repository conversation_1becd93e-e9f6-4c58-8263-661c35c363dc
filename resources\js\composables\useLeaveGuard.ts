import Swal from 'sweetalert2';
import { onBeforeUnmount, onMounted } from 'vue';

export function useLeaveGuard(shouldWarn: () => boolean) {
    const beforeUnloadHandler = (e: BeforeUnloadEvent) => {
        if (!shouldWarn()) return;

        e.preventDefault();
        e.returnValue = '';
    };

    const interceptNavigation = async (href: string | null) => {
        if (!href || !shouldWarn()) {
            if (href) window.location.href = href;
            return;
        }

        const result = await Swal.fire({
            title: 'Are you sure you want to leave?',
            text: 'Unsaved changes will be lost.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Leave',
            cancelButtonText: 'Stay',
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-red-600 hover:bg-red-500 text-white px-4 py-2 rounded',
                cancelButton: 'bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded ml-2',
            },
        });

        if (result.isConfirmed) {
            window.removeEventListener('beforeunload', beforeUnloadHandler);
            window.location.href = href;
        }
    };

    const clickHandler = (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        const clickable = target.closest('a[href], button[data-href]') as HTMLElement | null;

        if (!clickable) return;

        let href: string | null = null;

        if (clickable.tagName === 'A') {
            href = (clickable as HTMLAnchorElement).href;
        } else if (clickable.tagName === 'BUTTON') {
            href = clickable.getAttribute('data-href');
        }

        if (!href) return;

        e.preventDefault();
        e.stopImmediatePropagation();

        interceptNavigation(href);
    };

    onMounted(() => {
        window.addEventListener('beforeunload', beforeUnloadHandler);
        document.addEventListener('click', clickHandler, true);
    });

    onBeforeUnmount(() => {
        window.removeEventListener('beforeunload', beforeUnloadHandler);
        document.removeEventListener('click', clickHandler, true);
    });
}
