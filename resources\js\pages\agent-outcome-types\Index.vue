<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Button } from '@/components/ui/button';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { OutcomeType } from '@/types';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Props {
    outcomeTypes: PaginatedData<OutcomeType>;
    filters: FilterOptions & {
        name?: string;
    };
}

const props = defineProps<Props>();

const form = useForm({});

const outcomeType = ref(props.filters.name || '');

const debouncedSearch = useDebounceFn((value: string) => {
    form.get(route('agent-outcome-types.index', { name: value }), {
        preserveState: true,
        preserveScroll: true,
        replace: true,
    });
}, 300);

const handleSearch = (value: string) => {
    debouncedSearch(value);
};

const handleReset = () => {
    outcomeType.value = '';
    form.get(route('agent-outcome-types.index'));
};

const handleEdit = (outcomeType: OutcomeType) => {
    form.get(route('agent-outcome-types.edit', outcomeType.id));
};

const handleView = (outcomeType: OutcomeType) => {
    form.get(route('agent-outcome-types.show', outcomeType.id));
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';

    form.get(
        route('agent-outcome-types.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const handleDelete = (outcomeType: OutcomeType) => {
    submitWithConfirmation({
        form,
        confirmOptions: {
            html: `Are you sure you want to delete this agent outcome type? This action cannot be undone.`,
        },
        submitOptions: {
            method: 'delete',
            url: route('agent-outcome-types.destroy', outcomeType.id),
            transform: (data) => ({
                ...data,
            }),
            successMessage: `Agent outcome type deleted successfully.`,
            errorMessage: `Failed to delete agent outcome type. Please try again.`,
            entityName: 'agent outcome type',
        },
    });
};

const columns = [
    { field: 'name', label: 'Outcome Type', sortable: true, width: 'w-40' },
    { field: 'company.display_name', label: 'Company Name', sortable: true },
    {
        field: 'updated_at',
        label: 'Updated At',
        sortable: true,
        width: 'w-40',
        format: (value) => formatDateTime(value),
    },
    { field: 'updated_by.name', label: 'Updated By', sortable: true },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));
</script>

<template>
    <AppLayout>
        <Head title="Agent Outcome Type" />

        <div class="px-4 py-3">
            <Heading title="Agent Outcome Type" pageNumber="P000032" />

            <SearchCard
                v-model:searchValue="outcomeType"
                searchLabel="Outcome Type"
                searchPlaceholder="Outcome Type"
                @search="handleSearch"
                @reset="handleReset"
            >
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button @click="() => form.get(route('agent-outcome-types.create'))" class="bg-teal hover:bg-teal-hover flex items-center gap-2">
                        <FaIcon name="plus" />
                        Add New Agent Outcome Type
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="outcomeTypes.data"
                        :sort-state="sortState"
                        empty-message="No agent outcome type found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        @delete="handleDelete"
                        actionButtons=""
                        :showDeleteButton="true"
                        :showStatusToggle="false"
                    >
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries
                                    :from="outcomeTypes.from"
                                    :to="outcomeTypes.to"
                                    :total="outcomeTypes.total"
                                    entityName="outcomeTypes"
                                />
                            </div>
                            <Pagination :links="outcomeTypes.links" @navigate="handlePaginate" />
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
