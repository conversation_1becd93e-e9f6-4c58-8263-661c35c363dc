<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { AdminProfile, Company, Headquarter, Role, Team, User } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const { submitWithConfirmation } = useFormSubmit();
const { formatEnumOptions } = useFormatOptions();
const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess, hasTeamAccess } = useAuth();

interface Props {
    user: User;
    adminProfile: AdminProfile | null;
    roles: Role[];
    headquarters: Headquarter[];
    companies: Company[];
    teams: Team[];
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    username: props.user.username,
    email: props.user.email,
    password: '',
    password_confirmation: '',
    role: props.user.roles[0],
    status: props.user.status,
    headquarter_id: props.adminProfile?.headquarter_id || null,
    company_id: props.adminProfile?.company_id || null,
    team_id: props.adminProfile?.team_id || null,
    old_headquarter_id: props.adminProfile?.headquarter_id || null,
    old_company_id: props.adminProfile?.company_id || null,
    old_team_id: props.adminProfile?.team_id || null,
});

const isHeadquarterRole = computed(() => {
    return form.role ? props.roles.find((r) => r.id === form.role)?.is_headquarter : false;
});

const formFields = computed(() => {
    const fields = [];

    fields.push(
        {
            id: 'username',
            label: 'Username',
            type: 'show',
            required: false,
            modelValue: form.username,
            updateValue: (value: string) => (form.username = value),
        },
        {
            id: 'role',
            label: 'Role',
            type: 'show',
            required: false,
            modelValue: props.roles.find((r) => r.id === form.role)?.name || '',
        },
        {
            id: 'password',
            label: 'Password',
            type: 'password',
            required: false,
            placeholder: 'Password',
            error: form.errors.password,
            modelValue: form.password,
            updateValue: (value: string) => (form.password = value),
        },
        {
            id: 'password_confirmation',
            label: 'Confirm Password',
            type: 'password',
            required: false,
            placeholder: 'Confirm Password',
            error: form.errors.password_confirmation,
            modelValue: form.password_confirmation,
            updateValue: (value: string) => (form.password_confirmation = value),
        },
    );

    if (!hasHeadquarterAccess.value) {
        fields.push({
            id: 'headquarter_id',
            label: 'Headquarter Name',
            type: 'show',
            required: false,
            modelValue: props.headquarters.find((h) => h.id === form.headquarter_id)?.display_name || '',
        });
    }

    if (!hasCompanyAccess.value && !isHeadquarterRole.value && form.role !== null) {
        fields.push({
            id: 'company_id',
            label: 'Company Name',
            type: 'show',
            required: false,
            modelValue: props.companies.find((c) => c.id === form.company_id)?.display_name || '',
        });
    }

    if (!hasTeamAccess.value || (isHeadquarter && form.team_id !== null)) {
        fields.push({
            id: 'team_id',
            label: 'Team Name',
            type: 'show',
            required: false,
            modelValue: props.teams.find((t) => t.id === form.team_id)?.name || '',
        });
    }

    fields.push(
        {
            id: 'email',
            label: 'Email',
            type: 'input',
            required: false,
            placeholder: 'Email',
            error: form.errors.email,
            modelValue: form.email,
            updateValue: (value: string) => (form.email = value),
        },
        {
            id: 'status',
            label: 'Status',
            type: 'select',
            required: true,
            placeholder: 'Status',
            error: form.errors.status,
            options: formatEnumOptions(props.statuses),
            modelValue: form.status,
            updateValue: (value: number) => (form.status = value),
        },
    );

    return fields;
});

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'put',
            url: route('users.update', props.user.id),
            entityName: 'user',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Edit User" />
        <div class="px-4 py-3">
            <Heading title="User" pageNumber="P000015" description="Edit the selected user record" />

            <AppCard title="Edit User" :form="form" backRoute="users.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                                :class="field.class"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
