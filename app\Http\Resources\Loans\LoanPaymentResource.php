<?php

namespace App\Http\Resources\Loans;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * LoanPaymentResource
 *
 * Transforms LoanPayment model data for API responses.
 *
 * Usage examples:
 * - Single payment: new LoanPaymentResource($payment)
 * - Collection: LoanPaymentResource::collection($payments)
 */
class LoanPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $paymentTxnDetail = [];

        foreach ($this->paymentDetails as $paymentDetail) {
            $paymentTxnDetail[] = [
                'loan_txn_id' => $paymentDetail->loan_payment_id,
                'code' => $paymentDetail->loanTxn->code,
                'txn_type' => $paymentDetail->loanTxn->txn_type,
                'payment_type' => $this->payment_method,
                'amount' => $paymentDetail->amount,
                'remark' => $paymentDetail->loanTxn->remark,
            ];
        }

        $baseData = [
            'id' => $this->id,
            'code' => $this->code,
            'txn_date' => $this->txn_date,
            'txn_type' => $this->txn_type,
            'payment_detail' => $paymentTxnDetail,
            'payment_ref_code' => $this->payment_ref_code,
            'payment_method' => $this->payment_method,
            'payment_date' => $this->payment_date,
            'amount' => number_format($this->amount ?? 0, 2),
            'rebate_amount' => number_format($this->rebate_amount ?? 0, 2),
            'remark' => $this->remark,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];

        return $baseData;
    }
}
