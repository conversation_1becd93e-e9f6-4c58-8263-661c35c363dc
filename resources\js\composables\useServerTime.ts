import { onBeforeUnmount, onMounted, ref } from 'vue';

export function useServerTime() {
    const currentTime = ref('');
    const currentDate = ref('');
    let timeInterval: number | null = null;

    const updateTime = () => {
        const now = new Date();

        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const seconds = now.getSeconds().toString().padStart(2, '0');
        currentTime.value = `${hours}:${minutes}:${seconds}`;

        const options: Intl.DateTimeFormatOptions = {
            weekday: 'long',
            year: 'numeric',
            month: 'short',
            day: '2-digit',
        };
        currentDate.value = now.toLocaleDateString('en-US', options);
    };

    onMounted(() => {
        updateTime();
        timeInterval = window.setInterval(updateTime, 1000);
    });

    onBeforeUnmount(() => {
        if (timeInterval !== null) {
            clearInterval(timeInterval);
        }
    });

    return {
        currentTime,
        currentDate,
    };
}
