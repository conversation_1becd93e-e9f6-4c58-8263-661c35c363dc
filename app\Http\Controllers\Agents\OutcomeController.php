<?php

namespace App\Http\Controllers\Agents;

use App\Http\Controllers\Controller;
use App\Http\Requests\Agents\StoreOutcomeRequest;
use App\Http\Resources\Agents\AgentOutcomeResource;
use App\Models\AgentOutcome;
use App\Models\AgentOutcomeType;
use App\Models\AgentProfile;
use App\Models\Company;
use App\Models\Headquarter;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class OutcomeController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Display a listing of the agents.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', AgentOutcome::class);

        $query = AgentOutcome::forUser()
            ->withAuditUsers()
            ->with(['agent:id,name,display_name,code']);

        $this->applyRelationFilter($query, $request, 'agent_name', 'agent', 'name');
        $this->applySearchFilter($query, $request, 'outcome_type', 'outcome_types');
        $this->applySorting($query, $request, 'created_at', 'desc');

        $outcome = $this->applyPagination($query, $request, 10,
            fn ($outcome) => (new AgentOutcomeResource($outcome))->toArray($request));

        return Inertia::render('agent-outcome/Index', [
            'agentOutcome' => $outcome,
            'filters' => $request->only(['agent_name', 'outcome_type', 'per_page', 'sort_field', 'sort_direction']),
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
        ]);
    }

    /**
     * Show the form for creating a new agent outcome.
     */
    public function create(): Response
    {
        $this->authorize('create', AgentOutcome::class);

        return Inertia::render('agent-outcome/Create', [
            'agents' => AgentProfile::getForDropdown(),
            'outcomeTypes' => AgentOutcomeType::getForDropdown(),
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
        ]);
    }

    /**
     * Store a newly created agent in storage.
     */
    public function store(StoreOutcomeRequest $request): RedirectResponse
    {
        $this->authorize('create', AgentOutcome::class);

        try {
            DB::beginTransaction();

            $outcomeType = AgentOutcomeType::findOrFail($request->outcome_types_id);

            $agentOutcome = AgentOutcome::create([
                'company_id' => $request->company_id,
                'agent_id' => $request->agent_id,
                'outcome_types_id' => $outcomeType->id,
                'outcome_types' => $outcomeType->name,
                'amount' => $request->amount,
                'remark' => $request->remark,
            ]);

            $agentOutcome->companies()->attach($request->company_id);

            DB::commit();

            return Redirect::route('agent-outcomes.index')->with('success', 'Agent outcome created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            return Redirect::back()->withInput()->with('error', 'Failed to create agent outcome: '.$e->getMessage());
        }
    }

    /**
     * Display the specified agent outcome.
     */
    public function show(AgentOutcome $agentOutcome): Response
    {
        $this->authorize('view', $agentOutcome);

        $agentOutcome->withAuditUsers();
        $agentOutcome->load(['agent:id,name,display_name,code']);

        return Inertia::render('agent-outcome/Show', [
            'agentOutcome' => (new AgentOutcomeResource($agentOutcome))->toArray(request()),
        ]);
    }

    /**
     * Remove the specified agent outcome from storage.
     */
    public function destroy(AgentOutcome $agentOutcome): RedirectResponse
    {
        $this->authorize('delete', $agentOutcome);

        try {
            $agentOutcome->delete();

            return Redirect::route('agent-outcomes.index')->with('success', 'Agent outcome deleted successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to delete agent outcome: '.$e->getMessage());
        }
    }
}
