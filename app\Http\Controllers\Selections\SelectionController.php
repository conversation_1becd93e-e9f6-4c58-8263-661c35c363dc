<?php

namespace App\Http\Controllers\Selections;

use App\Enums\Selection\SelectionStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Selections\StoreSelectionRequest;
use App\Http\Requests\Selections\UpdateSelectionRequest;
use App\Http\Requests\Selections\UpdateStatusRequest;
use App\Http\Resources\Selections\SelectionResource;
use App\Models\Selection;
use App\Traits\QueryFilterableTrait;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;

class SelectionController extends Controller
{
    use QueryFilterableTrait;

    /**
     * Display a listing of the selections.
     *
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Selection::class);

        $query = Selection::query()->withAuditUsers();

        $this->applySearchFilter($query, $request, 'category');
        $this->applySearchFilter($query, $request, 'search', 'value');
        $this->applyStatusFilter($query, $request);
        $this->applySorting($query, $request);

        $selections = $this->applyPagination($query, $request, 10,
            fn ($selection) => (new SelectionResource($selection))->toArray($request));

        return Inertia::render('selections/Index', [
            'selections' => $selections,
            'categories' => Selection::getUniqueCategoryOptions(),
            'statuses' => SelectionStatus::options(),
            'filters' => $request->only(['category', 'search', 'status', 'per_page', 'sort_field', 'sort_direction']),
        ]);
    }

    /**
     * Show the form for creating a new selection.
     *
     * @return \Inertia\Response
     */
    public function create()
    {
        $this->authorize('create', Selection::class);

        return Inertia::render('selections/Create', [
            'categories' => Selection::getUniqueCategoryOptions(),
            'statuses' => SelectionStatus::options(),
            'defaultStatus' => SelectionStatus::ACTIVE->value,
        ]);
    }

    /**
     * Store a newly created selection in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(StoreSelectionRequest $request)
    {
        $this->authorize('create', Selection::class);

        try {
            Selection::create($request->validated());

            return redirect()->route('selections.index')->with('message', 'Selection created successfully');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Failed to create selection. '.$e->getMessage());
        }
    }

    /**
     * Display the specified selection.
     *
     * @return \Inertia\Response
     */
    public function show(Selection $selection)
    {
        $this->authorize('view', $selection);

        $selection->withAuditUsers();

        return Inertia::render('selections/Show', [
            'selection' => (new SelectionResource($selection))->toArray(request()),
        ]);
    }

    /**
     * Show the form for editing the specified selection.
     *
     * @return \Inertia\Response
     */
    public function edit(Selection $selection)
    {
        $this->authorize('update', $selection);

        return Inertia::render('selections/Edit', [
            'selection' => [
                'id' => $selection->id,
                'category' => $selection->category,
                'value' => $selection->value,
                'description' => $selection->description,
                'sort_order' => $selection->sort_order,
                'status' => $selection->status->value,
            ],
            'categories' => Selection::getUniqueCategoryOptions(),
            'statuses' => SelectionStatus::options(),
        ]);
    }

    /**
     * Update the specified selection in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(UpdateSelectionRequest $request, Selection $selection)
    {
        $this->authorize('update', $selection);

        try {
            $selection->update($request->validated());

            return redirect()->route('selections.index')->with('message', 'Selection updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with('error', 'Failed to update selection. '.$e->getMessage());
        }
    }

    /**
     * Remove the specified selection from storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Selection $selection)
    {
        $this->authorize('delete', $selection);

        try {
            $selection->delete();

            return redirect()->route('selections.index')->with('message', 'Selection deleted successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete selection. '.$e->getMessage());
        }
    }

    /**
     * Update the status of the specified resource.
     */
    public function updateStatus(UpdateStatusRequest $request, Selection $selection): RedirectResponse
    {
        $this->authorize('update', $selection);

        try {
            $selection->update(['status' => $request->validated('status')]);

            return Redirect::back()->with('success', 'selection status updated successfully.');
        } catch (\Exception $e) {
            return Redirect::back()->with('error', 'Failed to update selection status. '.$e->getMessage());
        }
    }
}
