/**
 * Composable for formatting various option types for dropdown components
 */
export function useFormatOptions() {
    /**
     * Formats an array of objects into option objects for dropdowns
     * @param items Array of objects to format
     * @param valueField Field to use as the option value (default: 'id')
     * @param labelField Field to use as the option label (default: 'value')
     * @returns Array of option objects with value and label properties
     */
    const formatSelectionOptions = <T extends Record<string, any>, V extends keyof T>(
        items: T[],
        valueField: keyof T = 'id' as keyof T,
        labelField: keyof T = 'value' as keyof T,
    ): Array<{ value: T[V]; label: string }> => {
        return items.map((item) => ({
            value: item[valueField],
            label: String(item[labelField]),
        }));
    };

    /**
     * Converts a record object to an array of option objects
     * @param options Record of option values and labels
     * @param valueType Type to convert the key to (default: 'number')
     * @returns Array of option objects with value and label properties
     */
    const formatEnumOptions = <T extends string | number | boolean>(
        options: Record<string | number, string>,
        valueType: 'string' | 'number' | 'boolean' = 'number',
    ): Array<{ value: T; label: string }> => {
        return Object.entries(options).map(([key, value]) => ({
            value: convertValue(key, valueType) as T,
            label: value,
        }));
    };

    /**
     * Converts a value to the specified type
     */
    const convertValue = (value: string | number, type: 'string' | 'number' | 'boolean'): string | number | boolean => {
        switch (type) {
            case 'string':
                return String(value);
            case 'number':
                return Number(value);
            case 'boolean':
                return Boolean(value);
            default:
                return value;
        }
    };

    return {
        formatSelectionOptions,
        formatEnumOptions,
    };
}
