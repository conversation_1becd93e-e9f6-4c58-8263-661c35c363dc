<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Inspiring;
use Illuminate\Http\Request;
use Inertia\Middleware;
use Tighten\Ziggy\Ziggy;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Get user data for sharing with frontend
     */
    protected function getUserData(Request $request): ?array
    {
        $user = $request->user();

        if (! $user) {
            return null;
        }

        $user->loadMissing(['roles:id,name,is_headquarter', 'adminProfiles.headquarters', 'adminProfiles.companies', 'adminProfiles.teams']);

        $adminProfileHierarchy = $user->getResolveHierarchy();

        return [
            'id' => $user->id,
            'username' => $user->username,
            'email' => $user->email,
            'roles' => $user->roles->pluck('name'),
            'permissions' => $user->isSuperAdmin() ? [] : $user->getAllPermissions()->pluck('name'),
            'is_headquarter' => $user->isHeadquarter(),
            'organization' => [
                'access' => [
                    'headquarter' => $adminProfileHierarchy['headquarter'] !== null,
                    'company' => $adminProfileHierarchy['company'] !== null,
                    'team' => $adminProfileHierarchy['team'] !== null,
                ],
                'primary' => [
                    'headquarter_id' => $adminProfileHierarchy['headquarter']?->id,
                    'company_id' => $adminProfileHierarchy['company']?->id,
                    'team_id' => $adminProfileHierarchy['team']?->id,
                ],
            ],
            'last_login_time' => $user->last_login_time,
        ];
    }

    /**
     * Get random inspirational quote
     */
    protected function getRandomQuote(): array
    {
        [$message, $author] = str(Inspiring::quotes()->random())->explode('-');

        return [
            'message' => trim($message),
            'author' => trim($author),
        ];
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        return [
            ...parent::share($request),
            'name' => config('app.name'),
            'version' => config('app.version', '1.0.0.0'),
            'quote' => $this->getRandomQuote(),
            'auth' => [
                'user' => $this->getUserData($request),
            ],
            'ziggy' => [
                ...(new Ziggy)->toArray(),
                'location' => $request->url(),
            ],
            'sidebarOpen' => $request->cookie('sidebar_state') !== 'false',
            'appearance' => $request->cookie('appearance') ?? 'light',
        ];
    }
}
