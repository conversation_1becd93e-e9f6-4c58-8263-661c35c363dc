<script setup lang="ts">
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Props {
    modelValue: string;
    options?: string[];
}

const props = withDefaults(defineProps<Props>(), {
    options: () => ['10', '25', '50', '100'],
});

const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
}>();

const handleChange = (value: string | null) => {
    if (value) {
        emit('update:modelValue', value);
    }
};
</script>

<template>
    <div class="flex items-center gap-2">
        <span class="text-sm text-gray-500">Show</span>
        <Select :model-value="modelValue" @update:model-value="handleChange">
            <SelectTrigger class="w-[70px]">
                <SelectValue :placeholder="modelValue" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem v-for="option in props.options" :key="option" :value="option">{{ option }}</SelectItem>
            </SelectContent>
        </Select>
    </div>
</template>
