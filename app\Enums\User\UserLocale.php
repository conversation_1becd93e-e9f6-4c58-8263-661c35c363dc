<?php

namespace App\Enums\User;

use App\Traits\EnumTrait;

enum UserLocale: string
{
    use EnumTrait;

    case ENGLISH = 'en';
    case CHINESE = 'zh';

    /**
     * Get all available locales as an array
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return [
            self::ENGLISH->value => 'English',
            self::CHINESE->value => 'Chinese',
        ];
    }

    /**
     * Get the display name for a locale
     */
    public function label(): string
    {
        return match ($this) {
            self::ENGLISH => 'English',
            self::CHINESE => 'Chinese',
        };
    }
}
