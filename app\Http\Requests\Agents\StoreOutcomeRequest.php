<?php

namespace App\Http\Requests\Agents;

use App\Http\Requests\BaseRequest;

class StoreOutcomeRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'agent_id' => ['required', 'integer'],
            'outcome_types_id' => ['required', 'integer'],
            'amount' => ['required', 'numeric', 'regex:/^\d{1,22}(\.\d{1,2})?$/', 'min:0.01'],
            'remark' => ['nullable', 'string', 'max:1000'],
        ];
    }
}
