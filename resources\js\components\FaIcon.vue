<script setup lang="ts">
import { FontAwesomeIcon } from '@/lib/fontawesome';
import { computed } from 'vue';

interface Props {
    name: string;
    size?: 'xs' | 'sm' | 'lg' | '2x' | '3x' | '4x';
    class?: string;
    spin?: boolean;
    pulse?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    size: 'lg',
    spin: false,
    pulse: false,
});

const classes = computed(() => `fa-icon ${props.class || ''}`);
</script>

<template>
    <FontAwesomeIcon :icon="['fas', name]" :class="classes" :size="size" :spin="spin" :pulse="pulse" />
</template>

<style scoped>
.fa-icon {
    display: inline-block;
    vertical-align: middle;
}
</style>
