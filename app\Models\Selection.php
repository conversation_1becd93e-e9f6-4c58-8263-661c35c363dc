<?php

namespace App\Models;

use App\Enums\Selection\SelectionStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Selection model for managing dropdown and selection options
 */
class Selection extends BaseModel
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'id',
        'uuid',
        'category',
        'value',
        'description',
        'sort_order',
        'status',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'status' => SelectionStatus::class,
            'sort_order' => 'integer',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get selections by category
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByCategory(string $category)
    {
        return self::where('category', $category)
            ->where('status', SelectionStatus::ACTIVE)
            ->orderBy('sort_order', 'asc')
            ->orderBy('value', 'asc')
            ->get();
    }

    /**
     * Get selections by multiple categories
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByCategories(array $categories)
    {
        return self::whereIn('category', $categories)
            ->where('status', SelectionStatus::ACTIVE)
            ->orderBy('category', 'asc')
            ->orderBy('sort_order', 'asc')
            ->orderBy('value', 'asc')
            ->get();
    }

    /**
     * Get selections as array of options for dropdown
     */
    public static function getOptionsForCategory(string $category): array
    {
        return self::where('category', $category)
            ->where('status', SelectionStatus::ACTIVE)
            ->orderBy('sort_order', 'asc')
            ->orderBy('value', 'asc')
            ->get()
            ->map(function ($selection) {
                return [
                    'id' => $selection->id,
                    'value' => $selection->value,
                ];
            })
            ->toArray();
    }

    /**
     * Get unique selections as array of options for dropdown
     */
    public static function getUniqueCategoryOptions(): array
    {
        $rows = self::select('category', 'sort_order', 'value')
            ->where('status', SelectionStatus::ACTIVE)
            ->whereRaw('id = (
                SELECT s2.id FROM selections s2
                WHERE s2.category = selections.category
                AND s2.status = selections.status
                AND s2.deleted_at IS NULL
                ORDER BY s2.sort_order ASC, s2.value ASC
                LIMIT 1
            )')
            ->orderBy('sort_order')
            ->orderBy('value')
            ->get();

        return $rows->map(fn ($row) => [
            'id' => $row->category,
            'value' => $row->category,
        ])->toArray();
    }
}
