<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ActionButton {
    icon: string;
    color: string;
    action: string;
    condition?: (row: any) => boolean;
    tooltip?: string;
}

interface Column {
    field: string;
    label: string;
    sortable?: boolean;
    width?: string;
    sticky?: boolean;
    align?: string;
    format?: (value: any, row: any) => string;
}

interface SortState {
    field: string | null;
    direction: 'asc' | 'desc';
}

interface Props {
    columns: Column[];
    data: any[];
    sortState: SortState;
    emptyMessage?: string;
    actionButtons?: ActionButton[];
    showViewButton?: boolean;
    showEditButton?: boolean;
    showDeleteButton?: boolean;
    showStatusToggle?: boolean;
    showRejectReason?: boolean;
    rejectReason?: string;
}

const props = withDefaults(defineProps<Props>(), {
    emptyMessage: 'No data found.',
    actionButtons: () => [],
    showViewButton: true,
    showEditButton: true,
    showDeleteButton: true,
    showStatusToggle: true,
    showRejectReason: false,
});

const emit = defineEmits(['sort', 'view', 'edit', 'delete', 'toggleStatus', 'action']);

// Simplified sort handler - just emit the field, let parent component handle direction
const handleSort = (field: string) => {
    if (!field) return;
    const column = props.columns.find((col) => col.field === field);
    if (!column || column.sortable !== true) return;

    // TODO: Temporary solution to change 'updated_by.name' to 'updated_by:name'
    field = field.replace(/\./, ':');

    emit('sort', field);
};

// Simple function to check if a column is sortable
const isSortable = (column: Column) => {
    return column.sortable === true;
};

// Get sort icons based on current sort state
const getSortIcon = (field: string) => {
    // TODO: Temporary solution to change 'updated_by.name' to 'updated_by:name'
    field = field.replace(/\./, ':');

    if (!props.sortState || props.sortState.field !== field) {
        return { asc: false, desc: false };
    }

    return {
        asc: props.sortState.direction === 'asc',
        desc: props.sortState.direction === 'desc',
    };
};

const getCellValue = (row: any, field: string) => {
    // Handle nested properties like 'updated_by.name'
    if (field.includes('.')) {
        const parts = field.split('.');
        let value = row;
        for (const part of parts) {
            value = value?.[part];
            if (value === undefined || value === null) return '-';
        }
        return value;
    }
    return row[field] !== undefined && row[field] !== null ? row[field] : '-';
};

const handleAction = (action: string, row: any) => {
    emit(action, row);
};

const handleCustomAction = (action: string, row: any) => {
    emit('action', { action, row });
};
</script>

<template>
    <div class="bg-white ring-1 ring-gray-900/5">
        <Table>
            <TableHeader>
                <TableRow class="bg-white">
                    <TableHead
                        v-for="column in columns"
                        :key="column.field"
                        :class="[
                            'border-sort border px-3 py-0 font-semibold text-black',
                            column.width || '',
                            column.sticky ? 'sticky right-0 bg-white' : '',
                            column.align || '',
                            isSortable(column) ? 'cursor-pointer hover:bg-gray-50' : '',
                        ]"
                    >
                        <!-- Use a button for better click handling -->
                        <button
                            v-if="isSortable(column)"
                            class="flex w-full items-center focus:outline-none"
                            @click="handleSort(column.field)"
                            type="button"
                        >
                            {{ column.label }}
                            <span class="ml-1 inline-flex flex-col leading-none">
                                <FaIcon
                                    name="sort-up"
                                    size="sm"
                                    class="mb-[1px]"
                                    :class="getSortIcon(column.field).asc ? 'text-black' : 'text-sort'"
                                />
                                <FaIcon
                                    name="sort-down"
                                    size="sm"
                                    class="mt-[-12px]"
                                    :class="getSortIcon(column.field).desc ? 'text-black' : 'text-sort'"
                                />
                            </span>
                        </button>
                        <span v-else>{{ column.label }}</span>
                    </TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <slot name="body" :data="data" :get-cell-value="getCellValue">
                    <TableRow
                        v-for="row in data"
                        :key="row.id || row.uuid"
                        class="group even:bg-cement last:!border-sort transition-colors last:!border-b-1 odd:bg-white"
                    >
                        <TableCell
                            v-for="column in columns"
                            :key="column.field"
                            :class="[
                                'border-sort border-x px-3 py-1.5',
                                column.width || '',
                                column.sticky ? 'group-even:bg-cement sticky right-0 group-odd:bg-white' : '',
                                column.align || '',
                            ]"
                        >
                            <slot :name="`cell-${column.field}`" :row="row" :value="getCellValue(row, column.field)">
                                <template v-if="column.field === 'actions'">
                                    <div class="flex items-center justify-center space-x-3">
                                        <TooltipProvider :delayDuration="800">
                                            <!-- Custom action buttons -->
                                            <Tooltip
                                                v-for="(button, index) in actionButtons"
                                                :key="index"
                                                v-show="button.condition ? button.condition(row) : true"
                                            >
                                                <TooltipTrigger as-child>
                                                    <a
                                                        @click="handleCustomAction(button.action, row)"
                                                        class="inline-flex h-6 w-6 cursor-pointer items-center justify-center"
                                                    >
                                                        <FaIcon :class="button.color" size="lg" :name="button.icon" />
                                                    </a>
                                                </TooltipTrigger>
                                                <TooltipContent>{{ button.tooltip }}</TooltipContent>
                                            </Tooltip>
                                            <!-- View button -->
                                            <Tooltip v-if="showViewButton">
                                                <TooltipTrigger as-child>
                                                    <a
                                                        @click="handleAction('view', row)"
                                                        class="inline-flex h-6 w-6 cursor-pointer items-center justify-center"
                                                    >
                                                        <FaIcon class="text-cobalt" size="lg" name="eye" />
                                                    </a>
                                                </TooltipTrigger>
                                                <TooltipContent>View</TooltipContent>
                                            </Tooltip>

                                            <!-- Edit button -->
                                            <Tooltip v-if="showEditButton">
                                                <TooltipTrigger as-child>
                                                    <a
                                                        @click="handleAction('edit', row)"
                                                        class="inline-flex h-6 w-6 cursor-pointer items-center justify-center"
                                                    >
                                                        <FaIcon class="text-teal" name="pen" />
                                                    </a>
                                                </TooltipTrigger>
                                                <TooltipContent>Edit</TooltipContent>
                                            </Tooltip>

                                            <!-- Status toggle buttons -->
                                            <template v-if="showStatusToggle">
                                                <Tooltip v-if="row.status === 0">
                                                    <TooltipTrigger as-child>
                                                        <a
                                                            @click="handleAction('toggleStatus', { row, newStatus: 1 })"
                                                            class="inline-flex h-6 w-6 cursor-pointer items-center justify-center"
                                                        >
                                                            <FaIcon class="text-canary" name="user-slash" />
                                                        </a>
                                                    </TooltipTrigger>
                                                    <TooltipContent>Deactivate</TooltipContent>
                                                </Tooltip>

                                                <Tooltip v-else>
                                                    <TooltipTrigger as-child>
                                                        <a
                                                            @click="handleAction('toggleStatus', { row, newStatus: 0 })"
                                                            class="inline-flex h-6 w-6 cursor-pointer items-center justify-center"
                                                        >
                                                            <FaIcon class="text-green" name="user" />
                                                        </a>
                                                    </TooltipTrigger>
                                                    <TooltipContent>Activate</TooltipContent>
                                                </Tooltip>
                                            </template>

                                            <!-- Delete button -->
                                            <Tooltip v-if="showDeleteButton">
                                                <TooltipTrigger as-child>
                                                    <a
                                                        @click="handleAction('delete', row)"
                                                        class="inline-flex h-6 w-6 cursor-pointer items-center justify-center"
                                                    >
                                                        <FaIcon class="text-red-500" name="trash" />
                                                    </a>
                                                </TooltipTrigger>
                                                <TooltipContent>Delete</TooltipContent>
                                            </Tooltip>

                                            <Tooltip v-if="showRejectReason">
                                                <TooltipTrigger as-child>
                                                    <FaIcon class="text-steel" name="note-sticky" />
                                                </TooltipTrigger>
                                                <TooltipContent>{{ props.rejectReason }}</TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                </template>
                                <template v-else>
                                    {{ column.format ? column.format(getCellValue(row, column.field), row) : getCellValue(row, column.field) }}
                                </template>
                            </slot>
                        </TableCell>
                    </TableRow>
                </slot>
                <slot name="footer"></slot>
                <TableRow v-if="!data.length">
                    <TableCell :colspan="columns.length" class="border-sort h-24 border text-center">
                        {{ emptyMessage }}
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </div>
</template>
