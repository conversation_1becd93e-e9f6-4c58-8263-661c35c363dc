<?php

namespace App\Models;

use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Support\Collection;

/**
 * AdminProfile model for managing admin user profiles
 */
class AdminProfile extends BaseModel
{
    use HasFactory, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'code',
        'contact_no',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the users associated with this admin profile.
     */
    public function users(): MorphToMany
    {
        return $this->morphToMany(User::class, 'userable');
    }

    /**
     * Get the headquarters associated with this admin profile.
     */
    public function headquarters(): MorphToMany
    {
        return $this->morphToMany(Headquarter::class, 'headquarterable');
    }

    /**
     * Get the companies associated with this admin profile.
     */
    public function companies(): MorphToMany
    {
        return $this->morphToMany(Company::class, 'companyable');
    }

    /**
     * Get the teams associated with this admin profile.
     */
    public function teams(): MorphToMany
    {
        return $this->morphToMany(Team::class, 'teamable');
    }

    /**
     * Get the headquarter associated with this admin profile.
     */
    public function headquarter(): ?Headquarter
    {
        return $this->companies()->whereNull('headquarter_id')->first();
    }

    /**
     * Get the company (non-headquarter) associated with this admin profile.
     */
    public function company(): ?Company
    {
        return $this->companies()->whereNotNull('headquarter_id')->first();
    }

    /**
     * Get the team associated with this admin profile.
     */
    public function team(): ?Team
    {
        return $this->teams()->first();
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new headquarter.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix(padLength: 5);
    }

    /**
     * Get accessible headquarter IDs for this profile.
     */
    public function getAccessibleHeadquarterIds(User $user): Collection
    {
        return $this->resolveHeadquarterIds($user);
    }

    /**
     * Get accessible company IDs for this profile.
     */
    public function getAccessibleCompanyIds(User $user): Collection
    {
        $companyIds = collect();

        // Direct companies
        if ($this->companies()->exists()) {
            $companyIds = $companyIds->merge($this->companies()->pluck('id'));
        }

        // Companies from teams
        if ($this->teams()->exists()) {
            $companyIds = $companyIds->merge(
                $this->teams()
                    ->with('companies:id')
                    ->get()
                    ->flatMap(fn ($team) => $team->companies->pluck('id'))
            );
        }

        // Companies from headquarters
        $headquarterIds = $this->resolveHeadquarterIds($user);

        if ($headquarterIds->isNotEmpty()) {
            $companyIds = $companyIds->merge(
                Company::whereIn('headquarter_id', $headquarterIds)->pluck('id')
            );
        }

        return $companyIds->unique()->values();
    }

    /**
     * Get accessible team IDs for this profile.
     */
    public function getAccessibleTeamIds(User $user): Collection
    {
        $teamIds = collect();

        // Direct teams
        if ($this->teams()->exists()) {
            $teamIds = $teamIds->merge($this->teams()->pluck('id'));
        }

        // Teams from companies
        if ($this->companies()->exists()) {
            $companyIds = $this->companies()->pluck('id');

            if ($companyIds->isNotEmpty()) {
                $companyTeamIds = Company::whereIn('id', $companyIds)
                    ->with('teams:id,company_id')
                    ->get()
                    ->flatMap(fn ($company) => $company->teams->pluck('id'));

                $teamIds = $teamIds->merge($companyTeamIds);
            }
        }

        // Teams from headquarters
        $headquarterIds = $this->resolveHeadquarterIds($user);

        if ($headquarterIds->isNotEmpty()) {
            $hqTeamIds = Headquarter::whereIn('id', $headquarterIds)
                ->with('companies.teams:id,company_id')
                ->get()
                ->flatMap(fn ($hq) => $hq->companies->flatMap(
                    fn ($company) => $company->teams->pluck('id')
                ));

            $teamIds = $teamIds->merge($hqTeamIds);
        }

        return $teamIds->unique()->values();
    }

    /**
     * Resolve all relevant headquarter IDs for the profile.
     */
    protected function resolveHeadquarterIds(User $user): Collection
    {
        $headquarterIds = collect();

        // Directly assigned headquarters
        if ($this->headquarters()->exists()) {
            $headquarterIds = $headquarterIds->merge($this->headquarters()->pluck('id'));
        }

        // Inferred from team → company → headquarter if user is HQ and none are assigned directly
        if ($user->isHeadquarter() && ! $this->headquarters()->exists() && $this->teams()->exists()) {
            $inferredHQIds = $this->teams()
                ->with('companies:headquarter_id,id')
                ->get()
                ->flatMap(fn ($team) => $team->companies->pluck('headquarter_id'));

            $headquarterIds = $headquarterIds->merge($inferredHQIds);
        }

        return $headquarterIds->unique()->values();
    }
}
