<?php

namespace Database\Seeders;

use Database\Seeders\AccessControl\PermissionSeeder;
use Database\Seeders\AccessControl\RoleSeeder;
use Database\Seeders\Agent\AgentOutcomeTypeSeeder;
use Database\Seeders\Agent\AgentSeeder;
use Database\Seeders\Company\CompanySeeder;
use Database\Seeders\Company\HeadquarterSeeder;
use Database\Seeders\Customer\CustomerSeeder;
use Database\Seeders\Loan\LoanTxnTypeSeeder;
use Database\Seeders\Selections\SelectionSeeder;
use Database\Seeders\Team\TeamSeeder;
use Database\Seeders\User\CompanyAdminUserSeeder;
use Database\Seeders\User\HeadquarterAdminUserSeeder;
use Database\Seeders\User\HeadquarterLoanApproverUserSeeder;
use Database\Seeders\User\HeadquarterLoanOfficerUserSeeder;
use Database\Seeders\User\HeadquarterLoanReviewerUserSeeder;
use Database\Seeders\User\SuperAdminSeeder;
use Database\Seeders\User\TeamAdminUserSeeder;
use Database\Seeders\User\TeamLoanApproverUserSeeder;
use Database\Seeders\User\TeamLoanOfficerUserSeeder;
use Database\Seeders\User\TeamLoanReviewerUserSeeder;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            RoleSeeder::class,
            PermissionSeeder::class,
            SelectionSeeder::class,
            LoanTxnTypeSeeder::class,
            SuperAdminSeeder::class,
        ]);

        if (config('app.env') === 'local') {
            $this->call([
                HeadquarterSeeder::class,
                CompanySeeder::class,
                TeamSeeder::class,
                HeadquarterAdminUserSeeder::class,
                HeadquarterLoanOfficerUserSeeder::class,
                HeadquarterLoanReviewerUserSeeder::class,
                HeadquarterLoanApproverUserSeeder::class,
                CompanyAdminUserSeeder::class,
                TeamAdminUserSeeder::class,
                TeamLoanOfficerUserSeeder::class,
                TeamLoanReviewerUserSeeder::class,
                TeamLoanApproverUserSeeder::class,
                AgentSeeder::class,
                AgentOutcomeTypeSeeder::class,
                CustomerSeeder::class,
            ]);
        }
    }
}
