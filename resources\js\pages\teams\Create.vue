<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useAuth } from '@/composables/useAuth';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Company, Headquarter, Selection } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { computed, onMounted, watch } from 'vue';

const { formatEnumOptions, formatSelectionOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();
const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess, primaryHeadquarterId, primaryCompanyId } = useAuth();

interface Props {
    headquarters: Headquarter[];
    companies: Company[];
    states: Selection[];
    countries: Selection[];
    telephoneCountries: Selection[];
    defaultStatus: number;
    statuses: Record<number, string>;
}

const props = defineProps<Props>();

const form = useForm({
    headquarter_id: hasHeadquarterAccess.value ? primaryHeadquarterId.value : null,
    code: '',
    name: '',
    email: '',
    website: '',
    selection_telephone_country_id: null as number | null,
    telephone: '',
    company_id: hasCompanyAccess.value ? primaryCompanyId.value : null,
    status: props.defaultStatus,
    line_1: '',
    line_2: '',
    postcode: '',
    city: '',
    selection_state_id: null as number | null,
    selection_country_id: null as number | null,
});

onMounted(() => {
    if (form.headquarter_id && !form.company_id) {
        if (isHeadquarter.value && form.headquarter_id) {
            const headquarterCompany = props.companies.find((company) => company.headquarter_id === form.headquarter_id && company.is_headquarter);

            if (headquarterCompany) {
                form.company_id = headquarterCompany.id;
            }
            return;
        }
    }
});

const formFields = computed(() => {
    const fields = [];

    fields.push(
        {
            id: 'name',
            label: 'Team Name',
            type: 'input',
            required: true,
            placeholder: 'Team Name',
            error: form.errors.name,
            modelValue: form.name,
            updateValue: (value: string) => (form.name = value),
        },
        {
            id: 'code',
            label: 'Team Code',
            type: 'input',
            required: true,
            placeholder: 'Team Code',
            error: form.errors.code,
            modelValue: form.code,
            updateValue: (value: string) => (form.code = value),
        },
        {
            id: 'telephone',
            label: 'Telephone No',
            type: 'inputSelect',
            required: false,
            placeholder: 'Telephone No',
            selectPlaceholder: 'Select Prefix',
            error: form.errors.telephone,
            modelValue: {
                select: form.selection_telephone_country_id,
                input: form.telephone,
            },
            updateValue: (value: { select: number; input: string }) => {
                form.telephone = value.input;
                form.selection_telephone_country_id = value.select;
            },
            options: formatSelectionOptions(props.telephoneCountries),
        },
    );

    if (!hasHeadquarterAccess.value) {
        fields.push({
            id: 'headquarter_id',
            label: 'Headquarter Name',
            type: 'select',
            required: true,
            placeholder: 'Headquarter Name',
            error: form.errors.headquarter_id,
            options: formatSelectionOptions(props.headquarters, 'id', 'display_name'),
            modelValue: form.headquarter_id,
            updateValue: (value: number) => (form.headquarter_id = value),
        });
    }

    if (!hasCompanyAccess.value && !isHeadquarter.value) {
        fields.push({
            id: 'company_id',
            label: 'Company Name',
            type: 'select',
            required: true,
            placeholder: 'Company Name',
            error: form.errors.company_id,
            options: formatSelectionOptions(filteredCompanies.value, 'id', 'display_name'),
            modelValue: form.company_id,
            updateValue: (value: number) => (form.company_id = value),
        });
    }

    fields.push(
        {
            id: 'email',
            label: 'Email Address',
            type: 'input',
            required: false,
            placeholder: 'Email Address',
            error: form.errors.email,
            modelValue: form.email,
            updateValue: (value: string) => (form.email = value),
        },
        {
            id: 'website',
            label: 'Website Address',
            type: 'input',
            required: false,
            placeholder: 'Website Address',
            error: form.errors.website,
            modelValue: form.website,
            updateValue: (value: string) => (form.website = value),
        },
        {
            id: 'line_1',
            label: 'Address Line 1',
            type: 'input',
            class: 'col-span-2',
            required: false,
            placeholder: 'Address Line 1',
            error: form.errors.line_1,
            modelValue: form.line_1,
            updateValue: (value: string) => (form.line_1 = value),
        },
        {
            id: 'line_2',
            label: 'Address Line 2',
            type: 'input',
            class: 'col-span-2',
            required: false,
            placeholder: 'Address Line 2',
            error: form.errors.line_2,
            modelValue: form.line_2,
            updateValue: (value: string) => (form.line_2 = value),
        },
        {
            id: 'postcode',
            label: 'Postcode',
            type: 'input',
            required: false,
            placeholder: 'Postcode',
            error: form.errors.postcode,
            modelValue: form.postcode,
            updateValue: (value: string) => (form.postcode = value),
        },
        {
            id: 'city',
            label: 'City',
            type: 'input',
            required: false,
            placeholder: 'City',
            error: form.errors.city,
            modelValue: form.city,
            updateValue: (value: string) => (form.city = value),
        },
        {
            id: 'state',
            label: 'State',
            type: 'stateSelect',
            required: false,
            placeholder: 'State',
            labelClass: 'mb-1',
            error: form.errors.selection_state_id,
            options: formatSelectionOptions(props.states),
            modelValue: form.selection_state_id,
            updateValue: (value: number) => (form.selection_state_id = value),
            filterByCountry: true,
            selectedCountryId: form.selection_country_id,
        },
        {
            id: 'country',
            label: 'Country',
            type: 'select',
            required: false,
            placeholder: 'Country',
            error: form.errors.selection_country_id,
            options: formatSelectionOptions(props.countries),
            modelValue: form.selection_country_id,
            updateValue: (value: number) => (form.selection_country_id = value),
        },
        {
            id: 'status',
            label: 'Status',
            type: 'select',
            required: true,
            placeholder: 'Status',
            error: form.errors.status,
            options: formatEnumOptions(props.statuses),
            modelValue: form.status,
            updateValue: (value: number) => (form.status = value),
        },
    );

    return fields;
});

const filteredCompanies = computed(() => {
    if (!form.headquarter_id) {
        return [];
    }
    return props.companies.filter((company) => company.headquarter_id === form.headquarter_id);
});

watch(
    () => form.headquarter_id,
    () => {
        form.company_id = null;
    },
);

watch(
    () => form.selection_country_id,
    (newCountryId, oldCountryId) => {
        if (newCountryId !== oldCountryId) {
            form.selection_state_id = null;
        }
    },
);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('teams.store'),
            entityName: 'team',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Create Team" />
        <div class="px-4 py-3">
            <Heading title="Team" pageNumber="P000010" description="Create a new team record" />

            <AppCard title="Add New Team" :form="form" backRoute="teams.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :labelClass="field.labelClass"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                                :class="field.class"
                                :filter-by-country="field.filterByCountry"
                                :selected-country-id="field.selectedCountryId"
                            />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
