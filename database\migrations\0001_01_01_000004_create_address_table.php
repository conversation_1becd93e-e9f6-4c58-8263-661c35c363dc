<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('addresses', function (Blueprint $table) {
            // Primary keys and identifiers
            $table->id();
            $table->uuid('uuid')->unique();

            // Polymorphic relation
            $table->morphs('addressable');

            // Address type and category
            $table->unsignedTinyInteger('category')->index();
            $table->foreignId('selection_type_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('type', 36)->nullable();

            // Organization details
            $table->string('company_name')->nullable();

            // Address details
            $table->text('line_1')->nullable();
            $table->text('line_2')->nullable();
            $table->string('postcode', 10)->nullable();
            $table->string('city', 50)->nullable();

            // Location references
            $table->foreignId('selection_state_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('state', 36)->nullable();
            $table->foreignId('selection_country_id')->nullable()->constrained('selections')->cascadeOnDelete();
            $table->string('country', 36)->nullable();

            // Address properties
            $table->boolean('is_primary')->default(false);

            // Audit fields
            $table->foreignId('created_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('updated_by')->nullable()->constrained('users')->nullOnDelete();
            $table->foreignId('deleted_by')->nullable()->constrained('users')->nullOnDelete();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('addresses');
    }
};
