<script setup lang="ts">
import AppCard from '@/components/card/AppCard.vue';
import FormField from '@/components/form/FormField.vue';
import Heading from '@/components/Heading.vue';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import type { Permission } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';
import { computed } from 'vue';

const { submitWithConfirmation } = useFormSubmit();

interface Props {
    permissions: Permission[];
}

const props = defineProps<Props>();

const form = useForm({
    name: '',
    level: 0,
    can_see_same_level: false,
    permissions: [] as number[],
});

const groupedPermissions = computed(() => {
    return props.permissions.reduce((groups: Record<string, Permission[]>, permission) => {
        let [action, resource] = permission.name.split(' ');

        action = action.replace(/-/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
        resource = resource.replace(/-/g, ' ');

        if (!groups[resource]) {
            groups[resource] = [];
        }
        groups[resource].push({
            ...permission,
            name: action,
        });
        return groups;
    }, {});
});

const handlePermissionToggle = (permissionId: number) => {
    const updatedPermissions = form.permissions.includes(permissionId)
        ? form.permissions.filter((id) => id !== permissionId)
        : [...form.permissions, permissionId];
    form.permissions = updatedPermissions;
};

const handleGroupToggle = (groupPermissions: Permission[]) => {
    const groupIds = groupPermissions.map((p) => p.id);
    const allSelected = groupPermissions.every((p) => form.permissions.includes(p.id));

    const updatedPermissions = allSelected
        ? form.permissions.filter((id) => !groupIds.includes(id))
        : [...new Set([...form.permissions, ...groupIds])];

    form.permissions = updatedPermissions;
};

const formFields = computed(() => [
    {
        id: 'name',
        label: 'Role Name',
        type: 'input',
        required: true,
        placeholder: 'Role Name',
        error: form.errors.name,
        modelValue: form.name,
        updateValue: (value: string) => (form.name = value),
    },
    {
        id: 'level',
        label: 'Role Level',
        type: 'input',
        required: true,
        placeholder: 'Role Level',
        error: form.errors.level,
        modelValue: form.level,
        remark: 'Higher levels have more privileges. Users can only assign roles with levels lower than their own.',
        updateValue: (value: number) => (form.level = value),
    },
    {
        id: 'can_see_same_level',
        label: 'Can See Same Level',
        type: 'checkbox',
        required: false,
        error: form.errors.can_see_same_level,
        modelValue: form.can_see_same_level,
        remark: 'If checked, users with this role can view others with the same role level.',
        updateValue: (value: boolean) => (form.can_see_same_level = value),
    },
]);

const submit = () => {
    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('roles.store'),
            entityName: 'role',
        },
    });
};
</script>

<template>
    <AppLayout>
        <Head title="Create Role" />
        <div class="px-4 py-3">
            <Heading title="Role" pageNumber="P000002" description="Create a new role record" />

            <AppCard title="Add New Role" :form="form" backRoute="roles.index" isForm @submit="submit">
                <section class="space-y-12">
                    <div class="space-y-6">
                        <div class="grid gap-4 lg:grid-cols-2">
                            <!-- Render form fields dynamically -->
                            <FormField
                                v-for="field in formFields"
                                :key="field.id"
                                :id="field.id"
                                :label="field.label"
                                :model-value="field.modelValue"
                                @update:model-value="field.updateValue"
                                :type="field.type"
                                :required="field.required"
                                :placeholder="field.placeholder"
                                :error="field.error"
                                :options="field.options"
                                :remark="field.remark"
                            />
                        </div>
                        <div class="grid gap-2">
                            <Label>Permissions</Label>
                            <div class="mt-4 space-y-4">
                                <div
                                    v-for="[resource, permissions] in Object.entries(groupedPermissions)"
                                    :key="resource"
                                    class="rounded-lg bg-gray-50 p-4"
                                >
                                    <div class="mb-4 flex items-center space-x-3">
                                        <input
                                            type="checkbox"
                                            :checked="permissions.every((p) => form.permissions.includes(p.id))"
                                            @change="handleGroupToggle(permissions)"
                                            class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                                        />
                                        <span class="text-sm font-medium text-gray-900 capitalize">
                                            {{ resource }}
                                        </span>
                                    </div>
                                    <div class="ml-7 grid grid-cols-1 gap-3 sm:grid-cols-3 lg:grid-cols-6">
                                        <label v-for="permission in permissions" :key="permission.id" class="flex items-center space-x-3 text-sm">
                                            <input
                                                type="checkbox"
                                                :checked="form.permissions.includes(permission.id)"
                                                @change="handlePermissionToggle(permission.id)"
                                                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                                            />
                                            <span class="text-gray-700">{{ permission.name }}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <InputError class="mt-1" :message="form.errors.permissions" />
                        </div>
                    </div>
                </section>
            </AppCard>
        </div>
    </AppLayout>
</template>
