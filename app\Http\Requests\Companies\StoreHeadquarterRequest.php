<?php

namespace App\Http\Requests\Companies;

use App\Http\Requests\BaseRequest;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class StoreHeadquarterRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'display_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('headquarters', 'name'),
            ],
            'business_registration_no' => ['nullable', 'string', 'max:100'],
            'old_business_registration_no' => ['nullable', 'string', 'max:100'],
            'status' => ['required', 'integer'],
            'logo' => ['nullable', 'file'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'display_name.required' => 'The headquarter name is required.',
            'display_name.unique' => 'This headquarter name is already in use.',
            'display_name.max' => 'The headquarter name cannot exceed 255 characters.',
            'status.required' => 'Please select a status for this headquarter.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if ($this->has('display_name')) {
                $this->merge([
                    'name' => Str::upper($this->display_name),
                ]);
            }
        });
    }
}
